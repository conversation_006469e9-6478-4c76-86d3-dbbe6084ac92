# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
Dockerfile
compose.yml
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions


# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
*.local.*

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*


# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.local-cache

# memory-bank
/memory-bank

local-data

.cursorrules
.cursor
*.ignore
.mcp-config.json
.next
.changeset
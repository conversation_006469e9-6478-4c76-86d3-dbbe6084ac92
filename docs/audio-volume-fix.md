# Audio Volume Consistency Fix

## Problem Description

The OpenAI Realtime Voice API was experiencing reduced audio output volume when the microphone was muted compared to when it was active. This created an inconsistent user experience where users had to adjust their system volume depending on whether they were speaking or just listening.

## Root Cause

The issue was caused by **WebRTC audio processing features** that browsers automatically enable when the microphone is active:

1. **Automatic Gain Control (AGC)** - Automatically adjusts microphone input levels
2. **Acoustic Echo Cancellation (AEC)** - Removes echo from audio streams  
3. **Noise Suppression** - Reduces background noise

When the microphone is active, these features are enabled and can affect the overall audio processing pipeline, including output volume. When the microphone is muted, these features are disabled, resulting in different audio characteristics and volume levels.

## Solution

The fix involves explicitly disabling WebRTC audio processing features to ensure consistent audio behavior regardless of microphone state.

### Changes Made

#### 1. Audio Constraints Configuration
```typescript
const audioConstraints = {
  // Device selection
  ...(selectedAudioInputDeviceId && { deviceId: { exact: selectedAudioInputDeviceId } }),
  // Disable WebRTC audio processing to prevent volume changes when mic state changes
  echoCancellation: false,
  autoGainControl: false,
  noiseSuppression: false,
  // Additional constraints to ensure consistent audio behavior
  sampleRate: 48000,
  channelCount: 1,
};
```

#### 2. Fallback Constraints
Ensured that fallback scenarios (when the selected audio device fails) also use the same constraints:

```typescript
const fallbackConstraints: MediaStreamConstraints = {
  audio: {
    echoCancellation: false,
    autoGainControl: false,
    noiseSuppression: false,
    sampleRate: 48000,
    channelCount: 1,
  },
};
```

#### 3. Audio Element Configuration
Enhanced the audio element configuration to prevent browser audio ducking:

```typescript
audioElement.current.volume = 1.0;
audioElement.current.muted = false;
audioElement.current.preservesPitch = false;
// Handle vendor-specific properties
const audioEl = audioElement.current as any;
if ('mozPreservesPitch' in audioEl) audioEl.mozPreservesPitch = false;
if ('webkitPreservesPitch' in audioEl) audioEl.webkitPreservesPitch = false;
```

#### 4. State Change Maintenance
Added a useEffect to maintain consistent audio settings across microphone state changes:

```typescript
useEffect(() => {
  if (audioElement.current) {
    // Keep audio volume at 100% and unmuted regardless of microphone state
    audioElement.current.volume = 1.0;
    audioElement.current.muted = false;
    // Maintain pitch preservation settings to prevent audio ducking
    audioElement.current.preservesPitch = false;
    const audioEl = audioElement.current as any;
    if ('mozPreservesPitch' in audioEl) audioEl.mozPreservesPitch = false;
    if ('webkitPreservesPitch' in audioEl) audioEl.webkitPreservesPitch = false;
  }
}, [isListening, isActive]);
```

## Browser-Specific Considerations

### Chrome
Chrome has additional flags that users can manually disable if needed:
- `chrome://flags/#webrtc-downmix-capture-audio-method`
- `chrome://flags/#chrome-wide-echo-cancellation`

### Firefox
Firefox users can disable WebRTC audio processing via `about:config`:
- `media.getusermedia.aec_enabled` → false
- `media.getusermedia.agc_enabled` → false  
- `media.getusermedia.noise_enabled` → false

However, our programmatic solution handles this automatically without requiring user intervention.

## Testing

A verification script is available to ensure all fixes are properly implemented:

```bash
npx tsx scripts/verify-audio-fix.ts
```

## Benefits

1. **Consistent Audio Volume** - Voice output maintains the same volume whether microphone is muted or active
2. **Better User Experience** - Users don't need to adjust system volume based on microphone state
3. **Cross-Browser Compatibility** - Works across different browsers and handles vendor-specific properties
4. **Automatic Handling** - No user configuration required

## Files Modified

- `src/lib/ai/speech/open-ai/use-voice-chat.openai.ts` - Main implementation
- `scripts/verify-audio-fix.ts` - Verification script
- `tests/voice-chat/audio-volume-consistency.spec.ts` - Test coverage

## References

- [WebRTC Audio Processing](https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints)
- [Audio Ducking in Browsers](https://wiki.archlinux.org/title/PulseAudio)
- [Chrome WebRTC Flags](https://chromium.googlesource.com/chromium/src/+/main/docs/webrtc_flags.md)

import { NextRequest } from "next/server";
import { getSession } from "auth/server";
import { chatRepository } from "lib/db/repository";
import { generateUUID } from "lib/utils";
import { UIMessage } from "ai";
import { ChatMetadata } from "app-types/chat";
import globalLogger from "logger";
import { z } from "zod";

const logger = globalLogger.withTag("voice-history-api");

// Schema for voice chat history request
const voiceHistoryRequestSchema = z.object({
  threadId: z.string().optional(), // If not provided, create new thread
  threadTitle: z.string().optional(),
  messages: z.array(z.object({
    id: z.string(),
    role: z.enum(["user", "assistant"]),
    parts: z.array(z.object({
      type: z.literal("text"),
      text: z.string()
    })),
    metadata: z.object({
      voiceModel: z.string().optional(),
      voice: z.string().optional(),
      isVoiceConversation: z.literal(true),
      agentId: z.string().optional(),
    }).optional()
  }))
});

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    
    if (!session?.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const json = await request.json();
    const { threadId, threadTitle, messages } = voiceHistoryRequestSchema.parse(json);

    logger.info(`Saving voice conversation history for user ${session.user.id}`);
    logger.info(`Thread ID: ${threadId || 'new'}, Messages: ${messages.length}`);

    let finalThreadId = threadId;

    // Create new thread if threadId not provided
    if (!finalThreadId) {
      finalThreadId = generateUUID();
      const newThread = await chatRepository.insertThread({
        id: finalThreadId,
        title: threadTitle || "Voice Conversation",
        userId: session.user.id,
      });
      logger.info(`Created new voice thread: ${newThread.id}`);
    } else {
      // Verify thread ownership
      const existingThread = await chatRepository.selectThread(finalThreadId);
      if (!existingThread || existingThread.userId !== session.user.id) {
        return new Response("Thread not found or access denied", { status: 403 });
      }
    }

    // Save all messages to the database
    const savedMessages = [];
    for (const message of messages) {
      const metadata: ChatMetadata = {
        ...message.metadata,
        isVoiceConversation: true,
      };

      const savedMessage = await chatRepository.upsertMessage({
        id: message.id,
        threadId: finalThreadId,
        role: message.role,
        parts: message.parts,
        metadata,
      });

      savedMessages.push(savedMessage);
      logger.info(`Saved voice message: ${message.id} (${message.role})`);
    }

    // Update thread title if it was auto-generated and we have messages
    if ((!threadTitle || threadTitle === "Voice Conversation") && messages.length > 0) {
      const firstUserMessage = messages.find(m => m.role === "user");
      if (firstUserMessage && firstUserMessage.parts[0]?.text) {
        const autoTitle = firstUserMessage.parts[0].text.substring(0, 50) + 
          (firstUserMessage.parts[0].text.length > 50 ? "..." : "");
        await chatRepository.updateThread(finalThreadId, { title: autoTitle });
        logger.info(`Updated thread title to: ${autoTitle}`);
      }
    }

    return new Response(JSON.stringify({
      success: true,
      threadId: finalThreadId,
      savedMessages: savedMessages.length
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });

  } catch (error) {
    logger.error("Error saving voice conversation history:", error);
    return new Response(
      JSON.stringify({ 
        error: "Failed to save voice conversation history",
        details: error instanceof Error ? error.message : String(error)
      }), 
      { 
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}

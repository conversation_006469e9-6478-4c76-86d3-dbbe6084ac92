"use client";

import { useState, useEffect, useCallback } from "react";

export interface AudioDevice {
  deviceId: string;
  label: string;
  groupId: string;
}

export interface UseAudioDevicesReturn {
  audioInputDevices: AudioDevice[];
  isLoading: boolean;
  error: string | null;
  refreshDevices: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
}

export function useAudioDevices(): UseAudioDevicesReturn {
  const [audioInputDevices, setAudioInputDevices] = useState<AudioDevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const enumerateDevices = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices
        .filter((device) => device.kind === "audioinput")
        .map((device) => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId,
        }));

      setAudioInputDevices(audioInputs);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to enumerate devices";
      setError(errorMessage);
      console.error("Error enumerating audio devices:", err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      // Request microphone permission to get device labels
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // Stop the stream immediately as we only needed it for permissions
      stream.getTracks().forEach((track) => track.stop());

      // Re-enumerate devices now that we have permissions
      await enumerateDevices();
      return true;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to request permissions";
      setError(errorMessage);
      console.error("Error requesting microphone permissions:", err);
      return false;
    }
  }, [enumerateDevices]);

  const refreshDevices = useCallback(async () => {
    await enumerateDevices();
  }, [enumerateDevices]);

  useEffect(() => {
    // Initial enumeration
    enumerateDevices();

    // Listen for device changes
    const handleDeviceChange = () => {
      enumerateDevices();
    };

    navigator.mediaDevices.addEventListener("devicechange", handleDeviceChange);

    return () => {
      navigator.mediaDevices.removeEventListener(
        "devicechange",
        handleDeviceChange,
      );
    };
  }, [enumerateDevices]);

  return {
    audioInputDevices,
    isLoading,
    error,
    refreshDevices,
    requestPermissions,
  };
}

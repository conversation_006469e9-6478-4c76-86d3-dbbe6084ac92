"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import {
  DEFAULT_VOICE_TOOLS,
  UIMessageWithCompleted,
  VoiceChatSession,
} from "..";
import { generateUUID, generateShortId } from "lib/utils";
import { TextPart, ToolUIPart } from "ai";
import {
  OpenAIRealtimeServerEvent,
  OpenAIRealtimeSession,
} from "./openai-realtime-event";

import { appStore } from "@/app/store";
import { useShallow } from "zustand/shallow";
import { useTheme } from "next-themes";
import { extractMCPToolId } from "lib/ai/mcp/mcp-tool-id";
import { callMcpToolByServerNameAction } from "@/app/api/mcp/actions";

export const OPENAI_VOICE = {
  Alloy: "alloy",
  Ballad: "ballad",
  Sage: "sage",
  Shimmer: "shimmer",
  Verse: "verse",
  Echo: "echo",
  Coral: "coral",
  Ash: "ash",
};

interface UseOpenAIVoiceChatProps {
  model?: string;
  voice?: string;
}

type Content =
  | {
      type: "text";
      text: string;
    }
  | {
      type: "tool-invocation";
      name: string;
      arguments: any;
      state: "call" | "result";
      toolCallId: string;
      result?: any;
    };

const createUIPart = (content: Content): TextPart | ToolUIPart => {
  if (content.type == "tool-invocation") {
    const part: ToolUIPart = {
      type: `tool-${content.name}`,
      input: content.arguments,
      state: "output-available",
      toolCallId: content.toolCallId,
      output: content.result,
    };
    return part;
  }
  return {
    type: "text",
    text: content.text,
  };
};

const createUIMessage = (m: {
  id?: string;
  role: "user" | "assistant";
  content: Content;
  completed?: boolean;
}): UIMessageWithCompleted => {
  const id = m.id ?? generateUUID();
  return {
    id,
    role: m.role,
    parts: [createUIPart(m.content)],
    completed: m.completed ?? false,
  };
};

export function useOpenAIVoiceChat(
  props?: UseOpenAIVoiceChatProps,
): VoiceChatSession {
  const { model = "gpt-realtime", voice = OPENAI_VOICE.Ash } = props || {};

  const [
    agentId,
    allowedAppDefaultToolkit,
    allowedMcpServers,
    selectedAudioInputDeviceId,
  ] = appStore(
    useShallow((state) => [
      state.voiceChat.agentId,
      state.allowedAppDefaultToolkit,
      state.allowedMcpServers,
      state.voiceChat.options.selectedAudioInputDeviceId,
    ]),
  );

  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const [isAssistantSpeaking, setIsAssistantSpeaking] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [messages, setMessages] = useState<UIMessageWithCompleted[]>([]);
  const [isMCPProcessing, setIsMCPProcessing] = useState(false);
  const [activeResponseId, setActiveResponseId] = useState<string | null>(null);
  const [isWaitingForResponseCompletion, setIsWaitingForResponseCompletion] =
    useState(false);
  const [hasVoiceInputReady, setHasVoiceInputReady] = useState(false);

  // Helper function to chunk long text into smaller pieces for OpenAI Realtime API
  const chunkText = useCallback(
    (text: string, maxLength: number = 8000): string[] => {
      if (text.length <= maxLength) {
        return [text];
      }

      const chunks: string[] = [];
      let currentIndex = 0;

      while (currentIndex < text.length) {
        let endIndex = currentIndex + maxLength;

        // If we're not at the end, try to break at a sentence or word boundary
        if (endIndex < text.length) {
          // Look for sentence endings first
          const sentenceEnd = text.lastIndexOf(".", endIndex);
          const questionEnd = text.lastIndexOf("?", endIndex);
          const exclamationEnd = text.lastIndexOf("!", endIndex);

          const bestSentenceEnd = Math.max(
            sentenceEnd,
            questionEnd,
            exclamationEnd,
          );

          if (bestSentenceEnd > currentIndex + maxLength * 0.5) {
            endIndex = bestSentenceEnd + 1;
          } else {
            // Fall back to word boundary
            const wordEnd = text.lastIndexOf(" ", endIndex);
            if (wordEnd > currentIndex + maxLength * 0.5) {
              endIndex = wordEnd;
            }
          }
        }

        chunks.push(text.slice(currentIndex, endIndex).trim());
        currentIndex = endIndex;
      }

      return chunks;
    },
    [],
  );

  // Helper function to wait for any active response to complete
  const waitForResponseCompletion = useCallback(() => {
    return new Promise<void>((resolve) => {
      if (isWaitingForResponseCompletion) {
        console.log("Already waiting for response completion, skipping");
        resolve();
        return;
      }

      setIsWaitingForResponseCompletion(true);
      const timeoutId: NodeJS.Timeout = setTimeout(() => {
        console.log(
          "Timeout waiting for response/audio completion, proceeding anyway",
        );
        dataChannel.current?.removeEventListener("message", handleEvent);
        setIsWaitingForResponseCompletion(false);
        resolve();
      }, 1000); // Reduced to 1 second since we're actively clearing audio

      const handleEvent = (event: MessageEvent) => {
        try {
          const serverEvent = JSON.parse(event.data);
          if (
            serverEvent.type === "response.done" ||
            serverEvent.type === "response.cancelled" ||
            serverEvent.type === "output_audio_buffer.cleared"
          ) {
            console.log(
              "Response/audio completion detected:",
              serverEvent.type,
            );
            dataChannel.current?.removeEventListener("message", handleEvent);
            clearTimeout(timeoutId);
            setIsWaitingForResponseCompletion(false);
            resolve();
          }
        } catch (_e) {
          // Ignore parsing errors
        }
      };

      // Listen for response completion
      dataChannel.current?.addEventListener("message", handleEvent);
    });
  }, [isWaitingForResponseCompletion]);
  const peerConnection = useRef<RTCPeerConnection | null>(null);
  const dataChannel = useRef<RTCDataChannel | null>(null);
  const audioElement = useRef<HTMLAudioElement | null>(null);
  const audioStream = useRef<MediaStream | null>(null);

  const { setTheme } = useTheme();
  const tracks = useRef<RTCRtpSender[]>([]);

  // Helper function to get audio constraints with device selection
  // FIX: Disable WebRTC audio processing to prevent volume reduction when microphone is muted
  // When WebRTC audio processing (AGC, AEC, noise suppression) is enabled, browsers can
  // automatically adjust audio output volume based on microphone state, causing the voice
  // output to be quieter when the mic is muted. Disabling these features ensures consistent
  // audio output volume regardless of microphone state.
  const getAudioConstraints = useCallback(() => {
    const audioConstraints = {
      // Device selection
      ...(selectedAudioInputDeviceId && { deviceId: { exact: selectedAudioInputDeviceId } }),
      // Disable WebRTC audio processing to prevent volume changes when mic state changes
      echoCancellation: false,
      autoGainControl: false,
      noiseSuppression: false,
      // Additional constraints to ensure consistent audio behavior
      sampleRate: 48000,
      channelCount: 1,
    };

    const constraints: MediaStreamConstraints = {
      audio: audioConstraints,
    };
    return constraints;
  }, [selectedAudioInputDeviceId]);

  // Helper function to get user media with fallback
  const getUserMediaWithFallback = useCallback(async () => {
    try {
      // Try with selected device first
      return await navigator.mediaDevices.getUserMedia(getAudioConstraints());
    } catch (err) {
      if (selectedAudioInputDeviceId) {
        console.warn(
          "Failed to access selected audio device, falling back to default:",
          err,
        );
        // Fallback to default device with same audio processing constraints
        const fallbackConstraints: MediaStreamConstraints = {
          audio: {
            echoCancellation: false,
            autoGainControl: false,
            noiseSuppression: false,
            sampleRate: 48000,
            channelCount: 1,
          },
        };
        return await navigator.mediaDevices.getUserMedia(fallbackConstraints);
      }
      throw err;
    }
  }, [getAudioConstraints, selectedAudioInputDeviceId]);

  const startListening = useCallback(async () => {
    try {
      if (!audioStream.current) {
        audioStream.current = await getUserMediaWithFallback();
      }
      if (tracks.current.length) {
        const micTrack = audioStream.current.getAudioTracks()[0];
        tracks.current.forEach((sender) => {
          sender.replaceTrack(micTrack);
        });
      }
      setIsListening(true);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  }, [getUserMediaWithFallback]);

  const stopListening = useCallback(async () => {
    try {
      if (audioStream.current) {
        audioStream.current.getTracks().forEach((track) => track.stop());
        audioStream.current = null;
      }
      if (tracks.current.length) {
        const placeholderTrack = createEmptyAudioTrack();
        tracks.current.forEach((sender) => {
          sender.replaceTrack(placeholderTrack);
        });
      }
      setIsListening(false);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  }, []);

  const createSession =
    useCallback(async (): Promise<OpenAIRealtimeSession> => {
      console.log("Creating voice chat session");
      console.log("Full request body:", {
        model,
        voice,
        allowedAppDefaultToolkit,
        allowedMcpServers,
        agentId,
      });

      const response = await fetch(
        `/api/chat/openai-realtime?model=${model}&voice=${voice}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model,
            voice,
            allowedAppDefaultToolkit,
            allowedMcpServers,
            agentId,
          }),
        },
      );
      if (response.status !== 200) {
        throw new Error(await response.text());
      }
      const session = await response.json();
      console.log("Received session response:", session);
      console.log(
        "Session instructions preview:",
        session.session?.instructions?.substring(0, 200),
      );

      if (session.error) {
        throw new Error(session.error.message);
      }

      return session;
    }, [model, voice, allowedAppDefaultToolkit, allowedMcpServers, agentId]);

  const updateUIMessage = useCallback(
    (
      id: string,
      action:
        | Partial<UIMessageWithCompleted>
        | ((
            message: UIMessageWithCompleted,
          ) => Partial<UIMessageWithCompleted>),
    ) => {
      setMessages((prev) => {
        if (prev.length) {
          const lastMessage = prev.find((m) => m.id == id);
          if (!lastMessage) return prev;
          const nextMessage =
            typeof action === "function" ? action(lastMessage) : action;
          if (lastMessage == nextMessage) return prev;
          return prev.map((m) => (m.id == id ? { ...m, ...nextMessage } : m));
        }
        return prev;
      });
    },
    [],
  );

  const clientFunctionCall = useCallback(
    async ({
      callId,
      toolName,
      args,
      id,
    }: { callId: string; toolName: string; args: string; id: string }) => {
      let toolResult: any = "success";
      stopListening();
      const toolArgs = JSON.parse(args);
      if (DEFAULT_VOICE_TOOLS.some((t) => t.name === toolName)) {
        switch (toolName) {
          case "changeBrowserTheme":
            setTheme(toolArgs?.theme);
            break;
        }
      } else {
        const toolId = extractMCPToolId(toolName);

        toolResult = await callMcpToolByServerNameAction(
          toolId.serverName,
          toolId.toolName,
          toolArgs,
        );
      }
      startListening();
      const resultText = JSON.stringify(toolResult).trim();

      // Step 1: Immediately interrupt any ongoing speech and audio playback
      console.log("Interrupting ongoing speech for MCP tool result");

      // Cancel response generation
      const cancelEvent = { type: "response.cancel" };
      dataChannel.current?.send(JSON.stringify(cancelEvent));

      // Clear audio buffer to immediately stop playback
      const clearAudioEvent = { type: "output_audio_buffer.clear" };
      dataChannel.current?.send(JSON.stringify(clearAudioEvent));

      console.log(
        "Sent response.cancel and output_audio_buffer.clear for tool result",
      );

      // Step 2: Add the function call output to the conversation (chunked if necessary)
      const textChunks = chunkText(resultText);
      const primaryChunk = textChunks[0];

      const functionOutputEvent = {
        type: "conversation.item.create",
        previous_item_id: id,
        item: {
          type: "function_call_output",
          call_id: callId,
          output: primaryChunk,
        },
      };

      updateUIMessage(id, (prev) => {
        const prevPart = prev.parts.find((p) => p.type == `tool-${toolName}`);
        if (!prevPart) return prev;
        const part: ToolUIPart = {
          state: "output-available",
          output: toolResult,
          toolCallId: callId,
          input: toolArgs,
          type: `tool-${toolName}`,
        };
        return {
          parts: [part],
        };
      });

      console.log(
        "Adding function call output to conversation:",
        functionOutputEvent,
      );
      dataChannel.current?.send(JSON.stringify(functionOutputEvent));

      // Add additional chunks as assistant messages if the response was long
      if (textChunks.length > 1) {
        for (let i = 1; i < textChunks.length; i++) {
          const additionalChunkEvent = {
            type: "conversation.item.create",
            item: {
              id: generateShortId(),
              type: "message",
              role: "assistant",
              content: [
                {
                  type: "text",
                  text: textChunks[i],
                },
              ],
            },
          };

          console.log(
            `Adding additional function result chunk ${i + 1}/${textChunks.length}:`,
            additionalChunkEvent,
          );
          dataChannel.current?.send(JSON.stringify(additionalChunkEvent));

          // Small delay between chunks
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      }

      // Step 3: Wait for any active response to complete, then create a new response
      waitForResponseCompletion().then(() => {
        if (!dataChannel.current) {
          console.warn("Data channel not available for tool response");
          return;
        }

        const responseCreateEvent = {
          type: "response.create",
          response: {
            modalities: ["audio", "text"],
            instructions: `You must immediately speak the content from the most recent function call output in the conversation. Speak it naturally and conversationally, as if it's your own strategic insight. Do not add any additional commentary or introduction - just speak the content directly.`,
            temperature: 0.8,
            max_output_tokens: "inf",
          },
        };

        console.log(
          "Creating new response to speak tool result:",
          responseCreateEvent,
        );
        dataChannel.current?.send(JSON.stringify(responseCreateEvent));
      });
    },
    [updateUIMessage, activeResponseId, chunkText, waitForResponseCompletion],
  );

  const manuallyTriggerMCP = useCallback(
    async (toolName?: string, input?: unknown) => {
      if (!dataChannel.current || !isActive) {
        console.warn("Cannot trigger MCP: No active connection");
        return;
      }

      if (isMCPProcessing || isWaitingForResponseCompletion) {
        console.warn(
          "MCP is already processing or waiting for response completion, please wait...",
        );
        return;
      }

      // Default to the openai_chat tool if no specific tool is provided
      // Use the correct MCP tool ID format: "serverName_toolName"
      const targetToolName = toolName || "openai-mcp_openai_chat";
      const assistantMessageId = generateShortId();

      console.log("Manual MCP trigger:", {
        targetToolName,
        assistantMessageId,
      });
      setIsMCPProcessing(true);

      // Gather current conversation history for MCP context
      const conversationHistory = messages
        .map((m) => {
          const textPart = m.parts.find((p) => p.type === "text") as TextPart;
          return `${m.role}: ${textPart?.text || ""}`;
        })
        .join("\n");

      // Default input for the openai_chat tool
      const defaultInput = {
        message:
          "Manual MCP trigger: Please provide strategic guidance for the current phase of the system design interview based on the conversation history.", // Meaningful message for manual trigger
        conversation_history: conversationHistory,
      };

      const toolInput = input || defaultInput;

      try {
        // Call MCP server directly to get the response
        const toolId = extractMCPToolId(targetToolName);
        console.log("Calling MCP server:", {
          serverName: toolId.serverName,
          toolName: toolId.toolName,
        });

        const mcpResponse = await callMcpToolByServerNameAction(
          toolId.serverName,
          toolId.toolName,
          toolInput,
        );

        console.log("MCP response received:", mcpResponse);

        // Extract the text content from the MCP response
        let responseText = "";
        if (typeof mcpResponse === "string") {
          responseText = mcpResponse;
        } else if (mcpResponse && typeof mcpResponse === "object") {
          // Handle MCP CallToolResult format
          if ("content" in mcpResponse && Array.isArray(mcpResponse.content)) {
            // Extract text from content array
            const textContent = mcpResponse.content
              .filter((item: any) => item?.type === "text" && item?.text)
              .map((item: any) => item.text)
              .join("\n");
            responseText = textContent || JSON.stringify(mcpResponse);
          } else if ("toolResult" in mcpResponse) {
            // Handle wrapped tool result
            responseText = String(mcpResponse.toolResult);
          } else {
            responseText = JSON.stringify(mcpResponse);
          }
        } else {
          responseText = String(mcpResponse);
        }

        // Add UI message to show the MCP response
        const uiMessage = createUIMessage({
          role: "assistant",
          id: assistantMessageId,
          content: {
            type: "text",
            text: responseText,
          },
          completed: true,
        });
        setMessages((prev) => [...prev, uiMessage]);

        // Step 1: Immediately interrupt any ongoing speech and audio playback
        console.log("Interrupting ongoing speech for MCP response");

        // Cancel response generation
        const cancelEvent = { type: "response.cancel" };
        dataChannel.current.send(JSON.stringify(cancelEvent));

        // Clear audio buffer to immediately stop playback
        const clearAudioEvent = { type: "output_audio_buffer.clear" };
        dataChannel.current.send(JSON.stringify(clearAudioEvent));

        console.log("Sent response.cancel and output_audio_buffer.clear");

        // Step 2: Add the MCP response as message items to the conversation (chunked if necessary)
        const textChunks = chunkText(responseText);

        for (let i = 0; i < textChunks.length; i++) {
          const chunkId = i === 0 ? assistantMessageId : generateShortId();
          const conversationItemEvent = {
            type: "conversation.item.create",
            item: {
              id: chunkId,
              type: "message",
              role: "assistant",
              content: [
                {
                  type: "text",
                  text: textChunks[i],
                },
              ],
            },
          };

          console.log(
            `Adding MCP response chunk ${i + 1}/${textChunks.length} to conversation:`,
            conversationItemEvent,
          );
          dataChannel.current.send(JSON.stringify(conversationItemEvent));

          // Small delay between chunks to ensure proper ordering
          if (i < textChunks.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 50));
          }
        }

        // Step 3: Wait for any active response to complete, then create a new response
        waitForResponseCompletion().then(() => {
          if (!dataChannel.current) {
            console.warn("Data channel not available for MCP response");
            return;
          }

          const responseCreateEvent = {
            type: "response.create",
            response: {
              modalities: ["audio", "text"],
              instructions: `You must immediately speak the content from the most recent assistant message in the conversation. Speak it naturally and conversationally, as if it's your own strategic insight. Do not add any additional commentary or introduction - just speak the content directly.`,
              temperature: 0.8,
              max_output_tokens: "inf",
            },
          };

          console.log(
            "Creating new response to speak MCP content:",
            responseCreateEvent,
          );
          dataChannel.current.send(JSON.stringify(responseCreateEvent));
        });

        console.log("MCP context injection completed successfully");
      } catch (error) {
        console.error("Error in manual MCP trigger:", error);

        // Add error message to UI
        const errorMessage = createUIMessage({
          role: "assistant",
          id: assistantMessageId,
          content: {
            type: "text",
            text: `Error calling MCP server: ${error instanceof Error ? error.message : String(error)}`,
          },
          completed: true,
        });
        setMessages((prev) => [...prev, errorMessage]);
      } finally {
        // Always reset the processing state
        setIsMCPProcessing(false);
      }
    },
    [
      dataChannel,
      isActive,
      messages,
      activeResponseId,
      chunkText,
      waitForResponseCompletion,
    ],
  );

  const handleServerEvent = useCallback(
    (event: OpenAIRealtimeServerEvent) => {
      switch (event.type) {
        case "input_audio_buffer.speech_started": {
          const message = createUIMessage({
            role: "user",
            id: event.item_id,
            content: {
              type: "text",
              text: "",
            },
          });
          setIsUserSpeaking(true);
          setMessages((prev) => [...prev, message]);
          break;
        }
        case "input_audio_buffer.committed": {
          updateUIMessage(event.item_id, {
            parts: [
              {
                type: "text",
                text: "",
              },
            ],
            completed: true,
          });
          setIsUserSpeaking(false);
          setHasVoiceInputReady(true); // Voice input is ready to be sent manually
          break;
        }
        case "conversation.item.input_audio_transcription.completed": {
          updateUIMessage(event.item_id, {
            parts: [
              {
                type: "text",
                text: event.transcript || "...speaking",
              },
            ],
            completed: true,
          });
          break;
        }
        case "response.audio_transcript.delta": {
          setIsAssistantSpeaking(true);
          setMessages((prev) => {
            const message = prev.findLast((m) => m.id == event.item_id)!;
            if (message) {
              return prev.map((m) =>
                m.id == event.item_id
                  ? {
                      ...m,
                      parts: [
                        {
                          type: "text",
                          text:
                            (message.parts[0] as TextPart).text! + event.delta,
                        },
                      ],
                    }
                  : m,
              );
            }
            return [
              ...prev,
              createUIMessage({
                role: "assistant",
                id: event.item_id,
                content: {
                  type: "text",
                  text: event.delta,
                },
                completed: true,
              }),
            ];
          });
          break;
        }
        case "response.audio_transcript.done": {
          updateUIMessage(event.item_id, (prev) => {
            const textPart = prev.parts.find((p) => p.type == "text");
            if (!textPart) return prev;
            textPart.text = event.transcript || "";
            return {
              ...prev,
              completed: true,
            };
          });
          break;
        }
        case "response.function_call_arguments.done": {
          const message = createUIMessage({
            role: "assistant",
            id: event.item_id,
            content: {
              type: "tool-invocation",
              name: event.name,
              arguments: JSON.parse(event.arguments),
              state: "call",
              toolCallId: event.call_id,
            },
            completed: true,
          });
          setMessages((prev) => [...prev, message]);
          clientFunctionCall({
            callId: event.call_id,
            toolName: event.name,
            args: event.arguments,
            id: event.item_id,
          });
          break;
        }
        case "input_audio_buffer.speech_stopped": {
          setIsUserSpeaking(false);
          break;
        }
        case "output_audio_buffer.stopped": {
          setIsAssistantSpeaking(false);
          break;
        }
        case "output_audio_buffer.cleared": {
          console.log("Audio buffer cleared - speech interrupted");
          setIsAssistantSpeaking(false);
          break;
        }
        case "response.created": {
          console.log("Response created:", event);
          setActiveResponseId(event.response_id);
          break;
        }
        case "response.done": {
          console.log("Response completed:", event);
          setIsAssistantSpeaking(false);
          setActiveResponseId(null);
          break;
        }
        case "response.cancelled": {
          console.log("Response cancelled:", event);
          setIsAssistantSpeaking(false);
          setActiveResponseId(null);
          break;
        }
        case "conversation.item.created": {
          console.log("Conversation item created:", event);
          break;
        }
        case "error": {
          console.error("OpenAI Realtime API error:", event.error);
          break;
        }
      }
    },
    [clientFunctionCall, updateUIMessage],
  );

  const start = useCallback(async () => {
    if (isActive || isLoading) return;
    setIsLoading(true);
    setError(null);
    setMessages([]);
    try {
      const session = await createSession();
      console.log({ session });
      const sessionToken = session.client_secret.value;
      const pc = new RTCPeerConnection();
      if (!audioElement.current) {
        audioElement.current = document.createElement("audio");
      }
      audioElement.current.autoplay = true;
      // Ensure consistent audio output regardless of microphone state
      audioElement.current.volume = 1.0;
      audioElement.current.muted = false;
      // Additional audio element properties to prevent browser audio ducking
      audioElement.current.preservesPitch = false;
      // Set vendor-specific properties safely
      const audioEl = audioElement.current as any;
      if ('mozPreservesPitch' in audioEl) audioEl.mozPreservesPitch = false;
      if ('webkitPreservesPitch' in audioEl) audioEl.webkitPreservesPitch = false;
      pc.ontrack = (e) => {
        if (audioElement.current) {
          audioElement.current.srcObject = e.streams[0];
        }
      };
      if (!audioStream.current) {
        audioStream.current = await getUserMediaWithFallback();
      }
      tracks.current = [];
      audioStream.current.getTracks().forEach((track) => {
        const sender = pc.addTrack(track, audioStream.current!);
        if (sender) tracks.current.push(sender);
      });

      const dc = pc.createDataChannel("oai-events");
      dataChannel.current = dc;
      dc.addEventListener("message", async (e) => {
        try {
          const event = JSON.parse(e.data) as OpenAIRealtimeServerEvent;
          handleServerEvent(event);
        } catch (err) {
          console.error({
            data: e.data,
            error: err,
          });
        }
      });
      dc.addEventListener("open", () => {
        setIsActive(true);
        setIsListening(true);
        setIsLoading(false);
      });
      dc.addEventListener("close", () => {
        setIsActive(false);
        setIsListening(false);
        setIsLoading(false);
      });
      dc.addEventListener("error", (errorEvent) => {
        console.error(errorEvent);
        setError(errorEvent.error);
        setIsActive(false);
        setIsListening(false);
      });
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      const sdpResponse = await fetch(`https://api.openai.com/v1/realtime`, {
        method: "POST",
        body: offer.sdp,
        headers: {
          Authorization: `Bearer ${sessionToken}`,
          "Content-Type": "application/sdp",
        },
      });
      const answer: RTCSessionDescriptionInit = {
        type: "answer",
        sdp: await sdpResponse.text(),
      };
      await pc.setRemoteDescription(answer);
      peerConnection.current = pc;
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
      setIsActive(false);
      setIsListening(false);
      setIsLoading(false);
    }
  }, [
    isActive,
    isLoading,
    createSession,
    handleServerEvent,
    voice,
    getUserMediaWithFallback,
  ]);

  const stop = useCallback(async () => {
    try {
      if (dataChannel.current) {
        dataChannel.current.close();
        dataChannel.current = null;
      }
      if (peerConnection.current) {
        peerConnection.current.close();
        peerConnection.current = null;
      }
      tracks.current = [];
      stopListening();
      setIsActive(false);
      setIsListening(false);
      setIsLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(String(err)));
    }
  }, [stopListening]);

  // Ensure audio element maintains consistent volume regardless of listening state
  useEffect(() => {
    if (audioElement.current) {
      // Keep audio volume at 100% and unmuted regardless of microphone state
      audioElement.current.volume = 1.0;
      audioElement.current.muted = false;
      // Maintain pitch preservation settings to prevent audio ducking
      audioElement.current.preservesPitch = false;
      const audioEl = audioElement.current as any;
      if ('mozPreservesPitch' in audioEl) audioEl.mozPreservesPitch = false;
      if ('webkitPreservesPitch' in audioEl) audioEl.webkitPreservesPitch = false;
    }
  }, [isListening, isActive]);

  useEffect(() => {
    return () => {
      stop();
    };
  }, [stop]);

  function createEmptyAudioTrack(): MediaStreamTrack {
    const audioContext = new AudioContext();
    const destination = audioContext.createMediaStreamDestination();
    return destination.stream.getAudioTracks()[0];
  }

  const manualSendInput = useCallback(() => {
    if (!dataChannel.current || !isActive) {
      console.warn("Cannot send input: No active connection");
      return;
    }

    console.log("Manually triggering response creation for voice input");

    // Reset the voice input ready state
    setHasVoiceInputReady(false);

    // Create a response to process the current voice input
    const responseCreateEvent = {
      type: "response.create",
      response: {
        modalities: ["audio", "text"],
        instructions: "Respond to the user's voice input naturally and conversationally.",
        temperature: 0.8,
        max_output_tokens: "inf",
      },
    };

    console.log("Creating response for manual voice input:", responseCreateEvent);
    dataChannel.current.send(JSON.stringify(responseCreateEvent));
  }, [dataChannel, isActive]);

  return {
    isActive,
    isUserSpeaking,
    isAssistantSpeaking,
    isListening,
    isLoading,
    error,
    messages,
    start,
    stop,
    startListening,
    stopListening,
    manuallyTriggerMCP,
    manualSendInput,
    hasVoiceInputReady,
  };
}

import { JSONSchema7 } from "json-schema";
import { tool as createTool } from "ai";
import { jsonSchemaToZod } from "lib/json-schema-to-zod";

const codeDescription = `JavaScript code that will be executed in the user's browser. You can use await directly since the code runs in an async context (e.g., await fetch('https://api.example.com/data')).

Use console.log, console.warn, and console.error to display execution results. Use \\n for line breaks to improve code readability for users.

Standard JavaScript APIs are available (fetch, JSON, Math, Date, Array methods, etc.). DOM manipulation, localStorage, and other browser security-restricted features are not available.`;

export const jsExecutionSchema: JSONSchema7 = {
  type: "object",
  properties: {
    code: {
      type: "string",
      description: codeDescription,
    },
  },
  required: ["code"],
};

export const jsExecutionTool = createTool({
  description: `Write and execute JavaScript code in the user's browser.
When users request code examples, use this tool to execute the code and show console.log results rather than just providing static code.
For data generation or random operations, executing code to generate actual data is more accurate than manually creating examples. This ensures users see real working results and can trust the output is genuinely generated by the code logic.`,

  inputSchema: jsonSchemaToZod(jsExecutionSchema),
});

{"id": "0b215ac1-bbb5-416c-a7cb-b4601e315b6c", "prevId": "0096de36-ab9c-4d94-b9d5-a541412ec342", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent": {"name": "agent", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "json", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "instructions": {"name": "instructions", "type": "json", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"agent_user_id_user_id_fk": {"name": "agent_user_id_user_id_fk", "tableFrom": "agent", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.archive_item": {"name": "archive_item", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "archive_id": {"name": "archive_id", "type": "uuid", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "added_at": {"name": "added_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"archive_item_item_id_idx": {"name": "archive_item_item_id_idx", "columns": [{"expression": "item_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"archive_item_archive_id_archive_id_fk": {"name": "archive_item_archive_id_archive_id_fk", "tableFrom": "archive_item", "tableTo": "archive", "columnsFrom": ["archive_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "archive_item_user_id_user_id_fk": {"name": "archive_item_user_id_user_id_fk", "tableFrom": "archive_item", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.archive": {"name": "archive", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"archive_user_id_user_id_fk": {"name": "archive_user_id_user_id_fk", "tableFrom": "archive", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bookmark": {"name": "bookmark", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "uuid", "primaryKey": false, "notNull": true}, "item_type": {"name": "item_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"bookmark_user_id_idx": {"name": "bookmark_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "bookmark_item_idx": {"name": "bookmark_item_idx", "columns": [{"expression": "item_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "item_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookmark_user_id_user_id_fk": {"name": "bookmark_user_id_user_id_fk", "tableFrom": "bookmark", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bookmark_user_id_item_id_item_type_unique": {"name": "bookmark_user_id_item_id_item_type_unique", "nullsNotDistinct": false, "columns": ["user_id", "item_id", "item_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_message": {"name": "chat_message", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "thread_id": {"name": "thread_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json[]", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"chat_message_thread_id_chat_thread_id_fk": {"name": "chat_message_thread_id_chat_thread_id_fk", "tableFrom": "chat_message", "tableTo": "chat_thread", "columnsFrom": ["thread_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_thread": {"name": "chat_thread", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"chat_thread_user_id_user_id_fk": {"name": "chat_thread_user_id_user_id_fk", "tableFrom": "chat_thread", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_oauth_session": {"name": "mcp_oauth_session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "mcp_server_id": {"name": "mcp_server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "server_url": {"name": "server_url", "type": "text", "primaryKey": false, "notNull": true}, "client_info": {"name": "client_info", "type": "json", "primaryKey": false, "notNull": false}, "tokens": {"name": "tokens", "type": "json", "primaryKey": false, "notNull": false}, "code_verifier": {"name": "code_verifier", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"mcp_oauth_session_server_id_idx": {"name": "mcp_oauth_session_server_id_idx", "columns": [{"expression": "mcp_server_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "mcp_oauth_session_state_idx": {"name": "mcp_oauth_session_state_idx", "columns": [{"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "mcp_oauth_session_tokens_idx": {"name": "mcp_oauth_session_tokens_idx", "columns": [{"expression": "mcp_server_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"mcp_oauth_session\".\"tokens\" is not null", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"mcp_oauth_session_mcp_server_id_mcp_server_id_fk": {"name": "mcp_oauth_session_mcp_server_id_mcp_server_id_fk", "tableFrom": "mcp_oauth_session", "tableTo": "mcp_server", "columnsFrom": ["mcp_server_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"mcp_oauth_session_state_unique": {"name": "mcp_oauth_session_state_unique", "nullsNotDistinct": false, "columns": ["state"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_server_custom_instructions": {"name": "mcp_server_custom_instructions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "mcp_server_id": {"name": "mcp_server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"mcp_server_custom_instructions_user_id_user_id_fk": {"name": "mcp_server_custom_instructions_user_id_user_id_fk", "tableFrom": "mcp_server_custom_instructions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "mcp_server_custom_instructions_mcp_server_id_mcp_server_id_fk": {"name": "mcp_server_custom_instructions_mcp_server_id_mcp_server_id_fk", "tableFrom": "mcp_server_custom_instructions", "tableTo": "mcp_server", "columnsFrom": ["mcp_server_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"mcp_server_custom_instructions_user_id_mcp_server_id_unique": {"name": "mcp_server_custom_instructions_user_id_mcp_server_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "mcp_server_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_server": {"name": "mcp_server", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.mcp_server_tool_custom_instructions": {"name": "mcp_server_tool_custom_instructions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": true}, "mcp_server_id": {"name": "mcp_server_id", "type": "uuid", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"mcp_server_tool_custom_instructions_user_id_user_id_fk": {"name": "mcp_server_tool_custom_instructions_user_id_user_id_fk", "tableFrom": "mcp_server_tool_custom_instructions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "mcp_server_tool_custom_instructions_mcp_server_id_mcp_server_id_fk": {"name": "mcp_server_tool_custom_instructions_mcp_server_id_mcp_server_id_fk", "tableFrom": "mcp_server_tool_custom_instructions", "tableTo": "mcp_server", "columnsFrom": ["mcp_server_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"mcp_server_tool_custom_instructions_user_id_tool_name_mcp_server_id_unique": {"name": "mcp_server_tool_custom_instructions_user_id_tool_name_mcp_server_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "tool_name", "mcp_server_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_edge": {"name": "workflow_edge", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true, "default": "'0.1.0'"}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "uuid", "primaryKey": false, "notNull": true}, "target": {"name": "target", "type": "uuid", "primaryKey": false, "notNull": true}, "ui_config": {"name": "ui_config", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"workflow_edge_workflow_id_workflow_id_fk": {"name": "workflow_edge_workflow_id_workflow_id_fk", "tableFrom": "workflow_edge", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_edge_source_workflow_node_id_fk": {"name": "workflow_edge_source_workflow_node_id_fk", "tableFrom": "workflow_edge", "tableTo": "workflow_node", "columnsFrom": ["source"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "workflow_edge_target_workflow_node_id_fk": {"name": "workflow_edge_target_workflow_node_id_fk", "tableFrom": "workflow_edge", "tableTo": "workflow_node", "columnsFrom": ["target"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_node": {"name": "workflow_node", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true, "default": "'0.1.0'"}, "workflow_id": {"name": "workflow_id", "type": "uuid", "primaryKey": false, "notNull": true}, "kind": {"name": "kind", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "ui_config": {"name": "ui_config", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "node_config": {"name": "node_config", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"workflow_node_kind_idx": {"name": "workflow_node_kind_idx", "columns": [{"expression": "kind", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_node_workflow_id_workflow_id_fk": {"name": "workflow_node_workflow_id_workflow_id_fk", "tableFrom": "workflow_node", "tableTo": "workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow": {"name": "workflow", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true, "default": "'0.1.0'"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "icon": {"name": "icon", "type": "json", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"workflow_user_id_user_id_fk": {"name": "workflow_user_id_user_id_fk", "tableFrom": "workflow", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
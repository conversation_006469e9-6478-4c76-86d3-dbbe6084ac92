realtime api only has a session state of 30 minutes - so around 25 minutes we need to implement a restart with all the context of the previous conversation in a new window
And when we say all the context of the existing conversation - I really mean it. Get the current conversation into a correctly formatted structure with user, assistant and retain it while you are ending the current session and starting a new session.
From the UI side - it is a must to actually cont


the voice from the realtime voice is getting reduced when the mic is muted - it is louder if the mic is not muted.

we should keep the voice conversation in history as well - just like we are keeping the pure text based system on the left side panel
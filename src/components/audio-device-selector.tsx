"use client";

import { useCallback } from "react";
import { <PERSON>, Mic, <PERSON>c<PERSON>ff, RefreshCw } from "lucide-react";
import { Button } from "ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "ui/dropdown-menu";
import { useAudioDevices } from "@/hooks/use-audio-devices";
import { appStore } from "@/app/store";
import { useShallow } from "zustand/shallow";
import { cn } from "lib/utils";

interface AudioDeviceSelectorProps {
  className?: string;
  variant?: "default" | "ghost" | "outline";
  size?: "default" | "sm" | "lg" | "icon";
}

export function AudioDeviceSelector({
  className,
  variant = "ghost",
  size = "icon",
}: AudioDeviceSelectorProps) {
  const [appStoreMutate, voiceChat, selectedAudioInputDeviceId] = appStore(
    useShallow((state) => [
      state.mutate,
      state.voiceChat,
      state.voiceChat.options.selectedAudioInputDeviceId,
    ]),
  );

  const {
    audioInputDevices,
    isLoading,
    error,
    refreshDevices,
    requestPermissions,
  } = useAudioDevices();

  const handleDeviceSelect = useCallback(
    (deviceId: string) => {
      appStoreMutate({
        voiceChat: {
          ...voiceChat,
          options: {
            ...voiceChat.options,
            selectedAudioInputDeviceId: deviceId,
          },
        },
      });
    },
    [appStoreMutate, voiceChat],
  );

  const handleRequestPermissions = useCallback(async () => {
    await requestPermissions();
  }, [requestPermissions]);

  const selectedDevice = audioInputDevices.find(
    (device) => device.deviceId === selectedAudioInputDeviceId,
  );

  const hasDevicesWithoutLabels = audioInputDevices.some(
    (device) => device.label.includes("Microphone") && device.label.length < 20,
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn("relative", className)}
          disabled={isLoading}
        >
          {error ? (
            <MicOff className="size-4 text-destructive" />
          ) : (
            <Mic className="size-4" />
          )}
          {isLoading && <RefreshCw className="absolute size-3 animate-spin" />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center justify-between">
          Audio Input Device
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshDevices}
            disabled={isLoading}
            className="h-6 px-2"
          >
            <RefreshCw className={cn("size-3", isLoading && "animate-spin")} />
          </Button>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {error && (
          <>
            <DropdownMenuItem disabled className="text-destructive text-xs">
              {error}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}

        {hasDevicesWithoutLabels && (
          <>
            <DropdownMenuItem
              onClick={handleRequestPermissions}
              className="text-xs text-muted-foreground"
            >
              <Mic className="size-3 mr-2" />
              Grant microphone access for device names
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}

        {audioInputDevices.length === 0 ? (
          <DropdownMenuItem disabled className="text-xs text-muted-foreground">
            No audio input devices found
          </DropdownMenuItem>
        ) : (
          <>
            <DropdownMenuItem
              onClick={() => handleDeviceSelect("")}
              className="text-xs"
            >
              <div className="flex items-center justify-between w-full">
                <span>System Default</span>
                {!selectedAudioInputDeviceId && (
                  <Check className="size-3 text-primary" />
                )}
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {audioInputDevices.map((device) => (
              <DropdownMenuItem
                key={device.deviceId}
                onClick={() => handleDeviceSelect(device.deviceId)}
                className="text-xs"
              >
                <div className="flex items-center justify-between w-full">
                  <span className="truncate">{device.label}</span>
                  {selectedAudioInputDeviceId === device.deviceId && (
                    <Check className="size-3 text-primary flex-shrink-0 ml-2" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </>
        )}

        {selectedDevice && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              disabled
              className="text-xs text-muted-foreground"
            >
              Selected: {selectedDevice.label}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

#!/usr/bin/env python3
"""Test script to verify MCP server works with better-chatbot format."""

import json
import subprocess
import sys
import time
import os


def test_mcp_server_format():
    """Test that our MCP server works with the expected format."""
    print("Testing MCP server with better-chatbot format...")
    
    # The configuration that should be sent to better-chatbot
    mcp_config = {
        "name": "openai-chat",
        "config": {
            "command": "uv",
            "args": [
                "--directory", 
                "/Users/<USER>/work/Instrument-2/mcp_openai", 
                "run", 
                "python", 
                "run_server.py"
            ]
        }
    }
    
    print("Configuration for better-chatbot:")
    print(json.dumps(mcp_config, indent=2))
    
    # Test that the command works
    print("\nTesting command execution...")
    try:
        # Change to the correct directory
        os.chdir("/Users/<USER>/work/Instrument-2/mcp_openai")
        
        # Test the command
        process = subprocess.Popen(
            ["uv", "run", "python", "run_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(3)
        
        # Check if process is running
        if process.poll() is None:
            print("✓ MCP server command works correctly")
            process.terminate()
            process.wait()
            
            print("\n" + "="*50)
            print("SUCCESS: Your MCP server is ready!")
            print("="*50)
            print("\nTo add it to better-chatbot:")
            print("1. Go to http://localhost:3000/mcp")
            print("2. Click 'Add MCP Server'")
            print("3. Use this configuration:")
            print(f"   Name: {mcp_config['name']}")
            print(f"   Config: {json.dumps(mcp_config['config'], indent=6)}")
            
            return True
        else:
            stdout, stderr = process.communicate()
            print("✗ MCP server failed to start")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing command: {e}")
        return False


def validate_config_format():
    """Validate that our config matches the expected schema."""
    print("Validating configuration format...")
    
    # This should match MCPStdioConfigZodSchema
    config = {
        "command": "uv",
        "args": [
            "--directory", 
            "/Users/<USER>/work/Instrument-2/mcp_openai", 
            "run", 
            "python", 
            "run_server.py"
        ]
    }
    
    # Check required fields
    if "command" not in config:
        print("✗ Missing 'command' field")
        return False
    
    if not isinstance(config["command"], str) or len(config["command"]) == 0:
        print("✗ 'command' must be a non-empty string")
        return False
    
    if "args" in config and not isinstance(config["args"], list):
        print("✗ 'args' must be an array")
        return False
    
    print("✓ Configuration format is valid")
    return True


def check_name_format():
    """Check that the server name follows the required format."""
    name = "openai-chat"
    
    # Must match /^[a-zA-Z0-9\-]+$/
    import re
    pattern = r'^[a-zA-Z0-9\-]+$'
    
    if re.match(pattern, name):
        print(f"✓ Server name '{name}' is valid")
        return True
    else:
        print(f"✗ Server name '{name}' is invalid (must contain only alphanumeric characters and hyphens)")
        return False


if __name__ == "__main__":
    print("Better-Chatbot MCP Integration Test")
    print("=" * 40)
    
    tests = [
        ("Name Format", check_name_format),
        ("Config Format", validate_config_format),
        ("Server Execution", test_mcp_server_format),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your MCP server is ready for better-chatbot.")
    else:
        print(f"\n❌ {len(results) - passed} test(s) failed.")
    
    sys.exit(0 if passed == len(results) else 1)

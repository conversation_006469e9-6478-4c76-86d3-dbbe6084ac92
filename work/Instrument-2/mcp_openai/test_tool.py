#!/usr/bin/env python3
"""Test the OpenAI chat tool directly."""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from mcp_openai_server.config import get_settings
from mcp_openai_server.openai_client import OpenAIClient


async def test_tool():
    """Test the OpenAI chat functionality."""
    print("Testing OpenAI chat tool...")
    
    try:
        # Load settings
        settings = get_settings()
        print(f"✓ Configuration loaded")
        print(f"  Model: {settings.openai_model}")
        print(f"  Max tokens: {settings.openai_max_tokens}")
        
        # Initialize client
        client = OpenAIClient(settings.openai_config)
        print("✓ OpenAI client initialized")
        
        # Test with a simple message (this will fail with test API key)
        print("\nTesting chat completion...")
        try:
            response = await client.chat_completion(
                message="Hello, how are you?",
                system_instructions="You are a helpful assistant."
            )
            print(f"✓ Response: {response}")
        except Exception as e:
            print(f"⚠ Expected error with test API key: {str(e)}")
            if "Invalid or missing OpenAI API key" in str(e):
                print("✓ Error handling working correctly")
            
        print("\n✓ Tool test completed successfully")
        
    except Exception as e:
        print(f"✗ Tool test failed: {e}")
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(test_tool())

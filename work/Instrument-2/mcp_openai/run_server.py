#!/usr/bin/env python3
"""Simple script to run the MCP OpenAI Server."""

import sys
import os
import asyncio
import logging
from typing import Any, Optional

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging to stderr (not stdout for MCP servers)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the MCP server."""
    try:
        # Import MCP components
        from mcp.server import Server
        from mcp.server.stdio import stdio_server
        from mcp.types import Tool, TextContent
        from mcp_openai_server.config import get_settings
        from mcp_openai_server.openai_client import OpenAIClient, OpenAIClientError

        # Initialize settings and clients
        try:
            settings = get_settings()
            openai_client = OpenAIClient(settings.openai_config)
        except Exception as e:
            logger.error(f"Configuration error: {str(e)}")
            sys.exit(1)

        # Create MCP server
        server = Server(settings.mcp_config.name)

        @server.list_tools()
        async def list_tools() -> list[Tool]:
            """List available tools."""
            return [
                Tool(
                    name="openai_chat",
                    description="Send a message and full conversation history to OpenAI's chat API via MCP for system design interview guidance",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "message": {
                                "type": "string",
                                "description": "The current user message or prompt indicating the context or request"
                            },
                            "conversation_history": {
                                "type": "string",
                                "description": "The full transcript of the interview conversation so far (required for accurate context and phase detection)"
                            },
                            "system_instructions": {
                                "type": "string",
                                "description": "Optional custom system instructions to override defaults"
                            }
                        },
                        "required": ["message", "conversation_history"]
                    }
                )
            ]

        @server.call_tool()
        async def call_tool(name: str, arguments: dict[str, Any]) -> list[TextContent]:
            """Handle tool calls."""
            if name != "openai_chat":
                raise ValueError(f"Unknown tool: {name}")

            try:
                message = arguments.get("message", "")
                conversation_history = arguments.get("conversation_history", "")
                system_instructions = arguments.get("system_instructions")

                logger.info(f"Processing chat request with message length: {len(message)}")

                # Validate input
                if not message or not message.strip():
                    return [TextContent(type="text", text="Error: Message cannot be empty")]
                if not conversation_history:
                    return [TextContent(type="text", text="Error: Conversation history must be provided")]

                # Use provided system instructions or fall back to default
                instructions = system_instructions or settings.system_instructions

                logger.debug(f"Using system instructions: {instructions[:100]}...")

                # Prepare input with full history
                full_message = f"Conversation History:\n{conversation_history}\n\nCurrent Message:\n{message.strip()}"

                # Call OpenAI API
                response = await openai_client.chat_completion(
                    message=full_message,
                    system_instructions=instructions
                )

                logger.info("Successfully processed chat request")
                return [TextContent(type="text", text=response)]

            except OpenAIClientError as e:
                error_msg = f"OpenAI API error: {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=f"Error: {error_msg}")]

            except Exception as e:
                error_msg = f"Unexpected error: {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=f"Error: {error_msg}")]

        logger.info(f"Starting MCP OpenAI Server v{settings.mcp_config.version}")
        logger.info(f"Using OpenAI model: {settings.openai_config.model}")

        # Test OpenAI connection
        logger.info("Testing OpenAI API connection...")
        if await openai_client.test_connection():
            logger.info("OpenAI API connection successful")
        else:
            logger.warning("OpenAI API connection test failed - server will still start")

        # Run the MCP server
        logger.info("Starting MCP server...")
        async with stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                server.create_initialization_options()
            )

    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

# MCP OpenAI Server

A simple Model Context Protocol (MCP) server that provides OpenAI chat functionality.

## Overview

This MCP server exposes a single tool that:
- Accepts input context from the MCP client
- Combines it with server-side system instructions
- Calls OpenAI's API for chat completion
- Returns the response to the client

The server acts as a bridge between MCP clients (like <PERSON>) and OpenAI's API, allowing you to leverage OpenAI's language models through the standardized MCP protocol.

## Features

- **Single Tool Interface**: `openai_chat` tool for AI-powered conversations
- **Configurable Models**: Support for any OpenAI model (GPT-4, GPT-3.5, etc.)
- **Flexible System Instructions**: Built-in defaults with per-request override capability
- **Comprehensive Error Handling**: Graceful handling of API failures and edge cases
- **Detailed Logging**: Full request/response logging for debugging
- **Easy Integration**: Works with Claude Desktop and other MCP clients

## Prerequisites

- Python 3.10 or higher
- OpenAI API key
- `uv` package manager (recommended) or `pip`

## Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd mcp_openai
```

### 2. Install Dependencies

Using `uv` (recommended):
```bash
uv sync
```

Using `pip`:
```bash
pip install -e .
```

### 3. Configure Environment

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` and set your OpenAI API key:
```bash
OPENAI_API_KEY=your_actual_openai_api_key_here
```

## Configuration

### Environment Variables

Set the following in your `.env` file:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `OPENAI_API_KEY` | Your OpenAI API key | - | ✅ |
| `OPENAI_MODEL` | OpenAI model to use | `gpt-4o-mini` | ❌ |
| `OPENAI_MAX_TOKENS` | Maximum tokens for responses | `1000` | ❌ |
| `OPENAI_TEMPERATURE` | Temperature (0.0-2.0) | `0.7` | ❌ |
| `MCP_SERVER_NAME` | MCP server name | `openai-chat` | ❌ |
| `MCP_SERVER_VERSION` | MCP server version | `0.1.0` | ❌ |
| `SYSTEM_INSTRUCTIONS` | Default system instructions | See below | ❌ |

### Default System Instructions

```
You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.
```

### Supported OpenAI Models

- `gpt-4o` - Latest GPT-4 Omni model
- `gpt-4o-mini` - Faster, cost-effective GPT-4 Omni
- `gpt-4-turbo` - GPT-4 Turbo
- `gpt-4` - Standard GPT-4
- `gpt-3.5-turbo` - GPT-3.5 Turbo
- Any other OpenAI chat completion model

## Usage

### Running the Server

#### Standalone
```bash
uv run python -m src.mcp_openai_server.server
```

#### With the run script
```bash
uv run python run_server.py
```

### Testing the Server

Run the test suite:
```bash
uv run python test_server.py
```

Test the tool directly:
```bash
uv run python test_tool.py
```

## MCP Client Integration

### Claude Desktop

1. Add the server to your Claude Desktop configuration file:

**macOS/Linux**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "openai-chat": {
      "command": "uv",
      "args": [
        "--directory",
        "/ABSOLUTE/PATH/TO/mcp_openai",
        "run",
        "python",
        "run_server.py"
      ]
    }
  }
}
```

2. Restart Claude Desktop
3. Look for the MCP tools icon in the interface
4. You should see the `openai_chat` tool available

### MCP Inspector

Test your server with the MCP Inspector:

```bash
npx @modelcontextprotocol/inspector uv run python run_server.py
```

## Tool Reference

### `openai_chat`

Sends a message and full conversation history to OpenAI's chat API via MCP for system design interview guidance.

**Parameters:**
- `message` (string, required): The current user message or prompt indicating the context or request
- `conversation_history` (string, required): The full transcript of the interview conversation so far (required for accurate context and phase detection)
- `system_instructions` (string, optional): Custom system instructions to override defaults

**Returns:**
- Success: The AI's response as a string, structured for the real-time AI to use verbatim
- Error: Error message prefixed with "Error: "

**Examples:**

Basic usage:
```json
{
  "name": "openai_chat",
  "arguments": {
    "message": "What should I focus on for the system design phase?",
    "conversation_history": "Interviewer: Let's start with a system design question. Design a URL shortener like bit.ly.\nCandidate: Great! I'll start by clarifying the requirements..."
  }
}
```

With custom system instructions:
```json
{
  "name": "openai_chat",
  "arguments": {
    "message": "How should I approach the database design?",
    "conversation_history": "Interviewer: Let's start with a system design question. Design a URL shortener like bit.ly.\nCandidate: Great! I'll start by clarifying the requirements...\nInterviewer: Good, now let's talk about the database design.",
    "system_instructions": "You are a senior software architect. Focus on database design best practices and scalability considerations."
  }
}
```

## Error Handling

The server handles various error conditions gracefully:

- **Invalid API Key**: Returns clear error message
- **Rate Limiting**: Indicates rate limit exceeded
- **Quota Exceeded**: Shows quota exhaustion
- **Network Issues**: Reports connection problems
- **Invalid Input**: Validates message content

All errors are logged to stderr and returned to the client with descriptive messages.

## Logging

The server logs to stderr (not stdout, which is reserved for MCP communication):

- **INFO**: General operation information
- **DEBUG**: Detailed request/response data
- **ERROR**: Error conditions and failures
- **WARNING**: Non-fatal issues

## Troubleshooting

### Common Issues

**Server won't start**
- Check your `.env` file exists and has valid `OPENAI_API_KEY`
- Ensure Python 3.10+ is installed
- Verify dependencies are installed: `uv sync`

**API key errors**
- Verify your OpenAI API key is correct
- Check your OpenAI account has sufficient credits
- Ensure the API key has appropriate permissions

**Claude Desktop integration issues**
- Use absolute paths in the configuration
- Restart Claude Desktop after configuration changes
- Check Claude Desktop logs for errors

**Tool not appearing in Claude Desktop**
- Verify the server starts without errors
- Check the MCP configuration syntax
- Ensure the server path is correct

### Debug Mode

Enable debug logging by modifying the logging level in `server.py`:

```python
logging.basicConfig(level=logging.DEBUG, ...)
```

## Development

### Project Structure

```
mcp_openai/
├── src/mcp_openai_server/
│   ├── __init__.py
│   ├── server.py          # Main MCP server
│   ├── config.py          # Configuration management
│   └── openai_client.py   # OpenAI API wrapper
├── test_server.py         # Test suite
├── test_tool.py          # Tool testing
├── run_server.py         # Server runner
├── .env                  # Environment configuration
├── .env.example          # Example configuration
├── pyproject.toml        # Project dependencies
└── README.md             # This file
```

### Adding Features

To extend the server:

1. Add new tools in `server.py` using the `@mcp.tool()` decorator
2. Update configuration in `config.py` if needed
3. Add tests in the test files
4. Update this README

## License

MIT License

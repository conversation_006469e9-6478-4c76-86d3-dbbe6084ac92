"""Configuration management for the MCP OpenAI Server."""

import os
from typing import Optional
from pydantic import BaseModel, Field
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class OpenAIConfig(BaseModel):
    """OpenAI API configuration."""
    
    api_key: str = Field(..., description="OpenAI API key")
    model: str = Field(default="gpt-4o-mini", description="OpenAI model to use")
    max_tokens: int = Field(default=1000, description="Maximum tokens for responses")
    temperature: float = Field(default=0.7, description="Temperature for response generation")


class MCPServerConfig(BaseModel):
    """MCP Server configuration."""
    
    name: str = Field(default="openai-chat", description="MCP server name")
    version: str = Field(default="0.1.0", description="MCP server version")


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # OpenAI settings
    openai_api_key: str = Field(..., description="OpenAI API key")
    openai_model: str = Field(default="gpt-4o-mini", description="OpenAI model to use")
    openai_max_tokens: int = Field(default=1000, description="Maximum tokens for responses")
    openai_temperature: float = Field(default=0.7, description="Temperature for response generation")
    
    # MCP Server settings
    mcp_server_name: str = Field(default="openai-chat", description="MCP server name")
    mcp_server_version: str = Field(default="0.1.0", description="MCP server version")
    
    # System instructions
    system_instructions: str = Field(
        default="You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.",
        description="Default system instructions for the AI"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @property
    def openai_config(self) -> OpenAIConfig:
        """Get OpenAI configuration."""
        return OpenAIConfig(
            api_key=self.openai_api_key,
            model=self.openai_model,
            max_tokens=self.openai_max_tokens,
            temperature=self.openai_temperature
        )
    
    @property
    def mcp_config(self) -> MCPServerConfig:
        """Get MCP server configuration."""
        return MCPServerConfig(
            name=self.mcp_server_name,
            version=self.mcp_server_version
        )


def get_settings() -> Settings:
    """Get application settings.

    Returns:
        Settings instance with loaded configuration

    Raises:
        ValueError: If required configuration is missing or invalid
    """
    try:
        settings = Settings()

        # Validate required settings
        if not settings.openai_api_key or settings.openai_api_key == "your_openai_api_key_here":
            raise ValueError(
                "OpenAI API key is required. Please set OPENAI_API_KEY in your .env file or environment variables."
            )

        if settings.openai_max_tokens <= 0:
            raise ValueError("OPENAI_MAX_TOKENS must be greater than 0")

        if not 0 <= settings.openai_temperature <= 2:
            raise ValueError("OPENAI_TEMPERATURE must be between 0 and 2")

        return settings

    except Exception as e:
        if isinstance(e, ValueError):
            raise
        raise ValueError(f"Configuration error: {str(e)}")

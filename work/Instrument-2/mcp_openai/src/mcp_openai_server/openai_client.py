"""OpenAI client wrapper for the MCP server."""

import logging
from typing import Optional, List, Dict, Any
from openai import As<PERSON><PERSON><PERSON><PERSON><PERSON>
from openai.types.chat import ChatCompletion

from .config import OpenAIConfig

logger = logging.getLogger(__name__)


class OpenAIClientError(Exception):
    """Custom exception for OpenAI client errors."""
    pass


class OpenAIClient:
    """Wrapper for OpenAI API client with error handling."""
    
    def __init__(self, config: OpenAIConfig):
        """Initialize the OpenAI client.
        
        Args:
            config: OpenAI configuration
        """
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
    
    async def chat_completion(
        self,
        message: str,
        system_instructions: Optional[str] = None,
        **kwargs: Any
    ) -> str:
        """Send a chat completion request to OpenAI.
        
        Args:
            message: User message to send
            system_instructions: Optional system instructions to override default
            **kwargs: Additional parameters to pass to the OpenAI API
            
        Returns:
            The AI's response as a string
            
        Raises:
            OpenAIClientError: If the API request fails
        """
        try:
            # Prepare messages
            messages: List[Dict[str, str]] = []
            
            if system_instructions:
                messages.append({
                    "role": "system",
                    "content": system_instructions
                })
            
            messages.append({
                "role": "user", 
                "content": message
            })
            
            # Prepare API parameters
            api_params = {
                "model": self.config.model,
                "messages": messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                **kwargs
            }
            
            logger.info(f"Sending chat completion request with model: {self.config.model}")
            logger.debug(f"Messages: {messages}")
            
            # Make the API call
            response: ChatCompletion = await self.client.chat.completions.create(**api_params)
            
            # Extract the response content
            if response.choices and response.choices[0].message.content:
                content = response.choices[0].message.content
                logger.info("Successfully received response from OpenAI")
                logger.debug(f"Response content length: {len(content)} characters")
                return content
            else:
                raise OpenAIClientError("No content in OpenAI response")
                
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            if "api_key" in str(e).lower():
                raise OpenAIClientError("Invalid or missing OpenAI API key")
            elif "rate_limit" in str(e).lower():
                raise OpenAIClientError("OpenAI API rate limit exceeded")
            elif "quota" in str(e).lower():
                raise OpenAIClientError("OpenAI API quota exceeded")
            else:
                raise OpenAIClientError(f"OpenAI API error: {str(e)}")
    
    async def test_connection(self) -> bool:
        """Test the connection to OpenAI API.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            await self.chat_completion("Hello", "Respond with just 'OK'")
            return True
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False

"""Main MCP server implementation."""

import asyncio
import logging
import sys
from typing import Any, Optional

from mcp.server.fastmcp import FastMCP
from mcp.server.models import InitializationOptions
from mcp.types import Tool, TextContent

from .config import get_settings
from .openai_client import OpenAIClient, OpenAIClientError

# Configure logging to stderr (not stdout for MCP servers)
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Initialize settings and clients
try:
    settings = get_settings()
    openai_client = OpenAIClient(settings.openai_config)
except Exception as e:
    logger.error(f"Configuration error: {str(e)}")
    sys.exit(1)

# Initialize FastMCP server
mcp = FastMCP(settings.mcp_config.name)


@mcp.tool()
async def openai_chat(
    message: str,
    conversation_history: str,
    system_instructions: Optional[str] = None
) -> str:
    """Send a message and full conversation history to OpenAI's chat API via MCP for system design interview guidance.

    This tool sends the user message along with the full conversation history to a powerful LLM (e.g., GPT-4.1 or GPT-5) via the MCP server. It is triggered at the start of each interview phase and for complex thinking tasks to provide strategic, phase-appropriate guidance in a conversational format.

    Args:
        message: The current user message or prompt indicating the context or request (required).
        conversation_history: The full transcript of the interview conversation so far (required for accurate context and phase detection).
        system_instructions: Optional custom system instructions to override defaults.

    Returns:
        The LLM's response as a string, structured for the real-time AI to use verbatim.

    Notes:
        - Always provide the full conversation_history to ensure the LLM can analyze the context and trigger appropriately for phases or heavy tasks.
        - MCP is invoked automatically based on the history for optimal guidance.
    """
    try:
        logger.info(f"Processing chat request with message length: {len(message)}")

        # Validate input
        if not message or not message.strip():
            return "Error: Message cannot be empty"
        if not conversation_history:
            return "Error: Conversation history must be provided"

        # Use provided system instructions or fall back to default
        instructions = system_instructions or settings.system_instructions

        logger.debug(f"Using system instructions: {instructions[:100]}...")

        # Prepare input with full history
        full_message = f"Conversation History:\n{conversation_history}\n\nCurrent Message:\n{message.strip()}"

        # Call OpenAI API
        response = await openai_client.chat_completion(
            message=full_message,
            system_instructions=instructions
        )

        logger.info("Successfully processed chat request")
        return response

    except OpenAIClientError as e:
        error_msg = f"OpenAI API error: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"

    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


async def main() -> None:
    """Main entry point for the MCP server."""
    try:
        logger.info(f"Starting MCP OpenAI Server v{settings.mcp_config.version}")
        logger.info(f"Using OpenAI model: {settings.openai_config.model}")

        # Test OpenAI connection
        logger.info("Testing OpenAI API connection...")
        if await openai_client.test_connection():
            logger.info("OpenAI API connection successful")
        else:
            logger.warning("OpenAI API connection test failed - server will still start")

        # Run the MCP server
        logger.info("Starting MCP server...")
        await mcp.run(transport="stdio")

    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)


def run_server():
    """Run the server, handling asyncio properly."""
    try:
        # Check if we're already in an asyncio event loop
        loop = asyncio.get_running_loop()
        logger.error("Cannot run server: already in an asyncio event loop")
        logger.info("This usually happens when the server is called from within another async context")
        logger.info("Try running the server directly: python -m src.mcp_openai_server.server")
        sys.exit(1)
    except RuntimeError:
        # No event loop running, safe to use asyncio.run()
        asyncio.run(main())


if __name__ == "__main__":
    run_server()

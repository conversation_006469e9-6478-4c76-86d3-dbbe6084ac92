# Quick Start Guide

Get your MCP OpenAI Server running in 5 minutes!

## Prerequisites

- Python 3.10+
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))
- `uv` package manager ([Install here](https://docs.astral.sh/uv/getting-started/installation/))

## 1. Setup (2 minutes)

```bash
# Clone and enter directory
git clone <repository-url>
cd mcp_openai

# Install dependencies
uv sync

# Configure environment
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:
```bash
OPENAI_API_KEY=your_actual_openai_api_key_here
```

## 2. Test (1 minute)

```bash
# Test the server
uv run python test_tool.py
```

You should see:
```
✓ Configuration loaded
✓ OpenAI client initialized
✓ Tool test completed successfully
```

## 3. Run Server (30 seconds)

```bash
# Start the MCP server
uv run python run_server.py
```

The server is now running and waiting for MCP client connections!

## 4. Connect to <PERSON> (2 minutes)

### macOS/Linux
Edit `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "openai-chat": {
      "command": "uv",
      "args": [
        "--directory",
        "/ABSOLUTE/PATH/TO/YOUR/mcp_openai",
        "run",
        "python",
        "run_server.py"
      ]
    }
  }
}
```

### Windows
Edit `%APPDATA%/Claude/claude_desktop_config.json` with the same content (use Windows paths).

**Important:** Replace `/ABSOLUTE/PATH/TO/YOUR/mcp_openai` with your actual path!

### Restart Claude Desktop

After saving the config, restart Claude Desktop completely.

## 5. Test in Claude Desktop (30 seconds)

1. Open Claude Desktop
2. Look for the tools icon (🔧) in the interface
3. You should see "openai_chat" tool available
4. Try asking: "Use the OpenAI tool to explain quantum physics"

## That's it! 🎉

Your MCP OpenAI Server is now running and connected to Claude Desktop.

## What's Next?

- **Customize**: Edit system instructions in `.env`
- **Explore**: Try different OpenAI models
- **Debug**: Use MCP Inspector for testing
- **Extend**: Add more tools to the server

## Need Help?

- Check the full [README.md](README.md) for detailed documentation
- See [USAGE_EXAMPLES.md](USAGE_EXAMPLES.md) for practical examples
- Review error messages in the terminal
- Verify your OpenAI API key and credits

## Common Issues

**"Configuration error"**: Check your `.env` file has a valid `OPENAI_API_KEY`

**"Tool not showing in Claude"**: 
- Use absolute paths in config
- Restart Claude Desktop
- Check server starts without errors

**"API key error"**: Verify your OpenAI API key is correct and has credits

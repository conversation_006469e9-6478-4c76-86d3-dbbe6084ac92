#!/usr/bin/env python3
"""Test script for the MCP OpenAI Server."""

import asyncio
import json
import subprocess
import sys
import time
from typing import Dict, Any


async def test_server_startup():
    """Test that the server starts up without errors."""
    print("Testing server startup...")
    
    try:
        # Start the server process
        process = subprocess.Popen(
            [sys.executable, "-m", "src.mcp_openai_server.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Give it a moment to start
        time.sleep(2)
        
        # Check if process is still running
        if process.poll() is None:
            print("✓ Server started successfully")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"✗ Server failed to start")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing server startup: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("Testing configuration...")
    
    try:
        from src.mcp_openai_server.config import get_settings
        
        settings = get_settings()
        print(f"✓ Configuration loaded successfully")
        print(f"  - OpenAI Model: {settings.openai_model}")
        print(f"  - Max Tokens: {settings.openai_max_tokens}")
        print(f"  - Temperature: {settings.openai_temperature}")
        print(f"  - Server Name: {settings.mcp_server_name}")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_openai_client():
    """Test OpenAI client initialization."""
    print("Testing OpenAI client...")
    
    try:
        from src.mcp_openai_server.config import get_settings
        from src.mcp_openai_server.openai_client import OpenAIClient
        
        settings = get_settings()
        client = OpenAIClient(settings.openai_config)
        print("✓ OpenAI client initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ OpenAI client test failed: {e}")
        return False


async def test_mcp_protocol():
    """Test basic MCP protocol communication."""
    print("Testing MCP protocol communication...")
    
    try:
        # Start the server process
        process = subprocess.Popen(
            [sys.executable, "-m", "src.mcp_openai_server.server"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Send initialization request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send the request
        process.stdin.write(json.dumps(init_request) + "\n")
        process.stdin.flush()
        
        # Give it time to process
        time.sleep(1)
        
        # Try to read response (non-blocking)
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        if "initialize" in stdout or "capabilities" in stdout:
            print("✓ MCP protocol communication working")
            return True
        else:
            print("✗ MCP protocol communication failed")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"✗ MCP protocol test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("=" * 50)
    print("MCP OpenAI Server Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("OpenAI Client", test_openai_client),
        ("Server Startup", test_server_startup),
        ("MCP Protocol", test_mcp_protocol),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
            
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())

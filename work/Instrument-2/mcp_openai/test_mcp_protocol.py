#!/usr/bin/env python3
"""Test the MCP server with JSON-RPC protocol."""

import json
import subprocess
import sys
import time
import threading
import queue

def test_mcp_server():
    """Test the MCP server with proper JSON-RPC communication."""
    print("Testing MCP server with JSON-RPC protocol...")
    
    try:
        # Start the server process
        process = subprocess.Popen(
            ["python", "run_server.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0
        )
        
        # Give it time to start
        time.sleep(2)
        
        # Send initialization request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {},
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("Sending initialization request...")
        request_json = json.dumps(init_request) + "\n"
        process.stdin.write(request_json)
        process.stdin.flush()
        
        # Read response with timeout
        def read_output(proc, q):
            try:
                line = proc.stdout.readline()
                if line:
                    q.put(line.strip())
            except:
                pass
        
        output_queue = queue.Queue()
        reader_thread = threading.Thread(target=read_output, args=(process, output_queue))
        reader_thread.daemon = True
        reader_thread.start()
        
        # Wait for response
        try:
            response = output_queue.get(timeout=5)
            print(f"Received response: {response}")
            
            # Try to parse as JSON
            try:
                response_data = json.loads(response)
                if "result" in response_data:
                    print("✓ Server responded correctly to initialization")
                    print(f"  Server capabilities: {response_data.get('result', {}).get('capabilities', {})}")
                    return True
                else:
                    print(f"✗ Unexpected response format: {response_data}")
                    return False
            except json.JSONDecodeError:
                print(f"✗ Response is not valid JSON: {response}")
                return False
                
        except queue.Empty:
            print("✗ No response received within timeout")
            return False
        
    except Exception as e:
        print(f"✗ Error testing server: {e}")
        return False
    finally:
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()

if __name__ == "__main__":
    success = test_mcp_server()
    sys.exit(0 if success else 1)

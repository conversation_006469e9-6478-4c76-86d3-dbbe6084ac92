import { test, expect } from "@playwright/test";

test.describe("Voice Chat Audio Volume Consistency", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the main chat page
    await page.goto("/");
    
    // Wait for the page to load
    await page.waitForLoadState("networkidle");
  });

  test("should maintain consistent audio volume regardless of microphone state", async ({
    page,
  }) => {
    // This test verifies that our WebRTC audio constraints fix is properly implemented
    // to prevent volume reduction when microphone is muted
    
    // Check if the voice chat component is available
    const voiceChatButton = page
      .locator(
        '[data-testid="voice-chat-button"], button:has-text("Voice Chat"), svg[data-lucide="audio-waveform"]',
      )
      .first();

    if (!(await voiceChatButton.isVisible())) {
      test.skip("Voice chat button not found - feature may not be available");
    }

    // Click to open voice chat
    await voiceChatButton.click();
    await page.waitForTimeout(1000);

    // Verify the voice chat interface is open
    const voiceChatInterface = page
      .locator(
        '[role="dialog"], .drawer-content, [data-testid="voice-chat-interface"]',
      )
      .first();

    expect(await voiceChatInterface.isVisible()).toBe(true);

    // Check that our audio constraints implementation is present in the code
    // We'll verify this by checking the browser's console for our specific constraints
    const hasCorrectAudioConstraints = await page.evaluate(() => {
      // Check if the implementation includes our WebRTC audio processing fixes
      const scriptContent = Array.from(document.scripts)
        .map((script) => script.textContent || "")
        .join(" ");

      return (
        scriptContent.includes("echoCancellation: false") &&
        scriptContent.includes("autoGainControl: false") &&
        scriptContent.includes("noiseSuppression: false")
      );
    });

    expect(hasCorrectAudioConstraints).toBe(true);
    console.log("✓ WebRTC audio processing constraints properly disabled");
  });

  test("should have proper audio element configuration", async ({ page }) => {
    // This test verifies that the audio element is configured to prevent volume ducking
    
    await page.goto("/");
    
    // Open voice chat
    const voiceChatButton = page
      .locator('svg[data-lucide="audio-waveform"]')
      .first();

    if (await voiceChatButton.isVisible()) {
      await voiceChatButton.click();
      await page.waitForTimeout(1000);

      // Check that our audio element configuration is present
      const hasCorrectAudioConfig = await page.evaluate(() => {
        const scriptContent = Array.from(document.scripts)
          .map((script) => script.textContent || "")
          .join(" ");

        return (
          scriptContent.includes("audioElement.current.volume = 1.0") &&
          scriptContent.includes("audioElement.current.muted = false") &&
          scriptContent.includes("preservesPitch = false")
        );
      });

      expect(hasCorrectAudioConfig).toBe(true);
      console.log("✓ Audio element configuration properly set to prevent ducking");
    } else {
      test.skip("Voice chat not available for testing");
    }
  });

  test("should use consistent audio constraints in fallback scenarios", async ({
    page,
  }) => {
    // This test verifies that even in fallback scenarios, we maintain the same audio constraints
    
    await page.goto("/");

    // Check that our fallback implementation also includes the correct constraints
    const hasFallbackConstraints = await page.evaluate(() => {
      const scriptContent = Array.from(document.scripts)
        .map((script) => script.textContent || "")
        .join(" ");

      // Look for fallback constraints in the getUserMediaWithFallback function
      return (
        scriptContent.includes("fallbackConstraints") &&
        scriptContent.includes("echoCancellation: false") &&
        scriptContent.includes("autoGainControl: false") &&
        scriptContent.includes("noiseSuppression: false")
      );
    });

    expect(hasFallbackConstraints).toBe(true);
    console.log("✓ Fallback audio constraints properly configured");
  });

  test("should maintain audio settings across microphone state changes", async ({
    page,
  }) => {
    // This test verifies that our useEffect properly maintains audio settings
    
    await page.goto("/");

    // Check that our useEffect implementation maintains consistent audio settings
    const hasConsistentAudioMaintenance = await page.evaluate(() => {
      const scriptContent = Array.from(document.scripts)
        .map((script) => script.textContent || "")
        .join(" ");

      return (
        scriptContent.includes("useEffect") &&
        scriptContent.includes("audioElement.current.volume = 1.0") &&
        scriptContent.includes("isListening") &&
        scriptContent.includes("isActive")
      );
    });

    expect(hasConsistentAudioMaintenance).toBe(true);
    console.log("✓ Audio settings maintenance across state changes properly implemented");
  });
});

test.describe("WebRTC Audio Processing Fix Verification", () => {
  test("should have comprehensive audio constraints documentation", async ({
    page,
  }) => {
    // This test verifies that our fix is properly documented
    
    await page.goto("/");

    const hasProperDocumentation = await page.evaluate(() => {
      const scriptContent = Array.from(document.scripts)
        .map((script) => script.textContent || "")
        .join(" ");

      return (
        scriptContent.includes("FIX: Disable WebRTC audio processing") &&
        scriptContent.includes("prevent volume reduction when microphone is muted") &&
        scriptContent.includes("AGC, AEC, noise suppression")
      );
    });

    expect(hasProperDocumentation).toBe(true);
    console.log("✓ Audio volume fix properly documented");
  });

  test("should include sample rate and channel configuration", async ({
    page,
  }) => {
    // This test verifies that we set proper audio quality constraints
    
    await page.goto("/");

    const hasAudioQualityConstraints = await page.evaluate(() => {
      const scriptContent = Array.from(document.scripts)
        .map((script) => script.textContent || "")
        .join(" ");

      return (
        scriptContent.includes("sampleRate: 48000") &&
        scriptContent.includes("channelCount: 1")
      );
    });

    expect(hasAudioQualityConstraints).toBe(true);
    console.log("✓ Audio quality constraints properly configured");
  });
});

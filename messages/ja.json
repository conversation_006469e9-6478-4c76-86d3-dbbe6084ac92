{"Common": {"cancel": "キャンセル", "update": "更新", "continue": "続行", "success": "成功", "delete": "削除", "save": "保存", "back": "戻る", "next": "次へ", "create": "作成", "showLess": "少なく表示", "showMore": "もっと表示", "generate": "生成", "edit": "編集", "editAgent": "エージェントを編集", "search": "検索...", "approve": "承認", "reject": "拒否", "saving": "保存中...", "optional": "オプション", "deleting": "削除中...", "run": "実行", "description": "説明", "defaultValue": "デフォルト値", "empty": "空", "required": "必須", "options": "オプション", "status": "ステータス", "result": "結果", "startedAt": "開始時刻", "duration": "所要時間", "addOption": "オプションを追加", "tool": "ツール", "selectTool": "ツールを選択...", "noResults": "結果がありません。", "generateInputWithAI": "AIで入力を生成", "generatingInputWithAI": "AIで入力を生成中...", "inputGeneratedSuccessfully": "入力が正常に生成されました", "failedToGenerateInput": "入力の生成に失敗しました", "createWithExample": "例で作成", "resultsFound": "{count}件の結果が見つかりました", "youAreAnExpertIn": "あなたは{role}の専門家です", "sharedBy": "{userName}さんが共有"}, "Auth": {"SignIn": {"title": "おかえりなさい", "description": "アカウントにサインインして続行", "oauthClientIdNotSet": "{provider}のクライアントIDが設定されていません", "noAccount": "アカウントをお持ちでない方は", "signUp": "サインアップ", "signIn": "サインイン", "orContinueWith": "または次で続行"}, "SignUp": {"title": "アカウントを作成", "signIn": "サインイン", "description": "アカウントにサインアップ", "step1": "メールアドレスを入力して、私たちとの旅を始めましょう", "step2": "チャット時にこの名前を使用します", "step3": "アカウントを保護するための強力なパスワードを作成してください", "signUp": "サインアップ", "invalidEmail": "無効なメールアドレス", "emailAlreadyExists": "メールアドレスが既に存在します", "nameRequired": "名前が必要です", "passwordRequired": "パスワードが必要です", "createAccount": "アカウントを作成"}, "Intro": {"description": "better-chatbot へようこそ。AIを活用した会話ツールを体験するためにサインインしてください。"}}, "Chat": {"Error": "チャットエラー", "thisMessageWasNotSavedPleaseTryTheChatAgain": "このメッセージは保存されませんでした。もう一度チャットをお試しください。", "Greeting": {"goodMorning": "おはようございます、{name}さん", "goodAfternoon": "こんにちは、{name}さん", "goodEvening": "こんばんは、{name}さん", "niceToSeeYouAgain": "またお会いできて嬉しいです、{name}さん", "whatAreYouWorkingOnToday": "今日は何に取り組んでいますか、{name}さん？", "letMeKnowWhenYoureReadyToBegin": "準備ができたら教えてください。", "whatAreYourThoughtsToday": "今日はどんなことを考えていますか？", "whereWouldYouLikeToStart": "どこから始めたいですか？", "whatAreYouThinking": "何を考えていますか、{name}さん？"}, "TemporaryChat": {"toggleTemporaryChat": "一時チャットの切り替え", "temporaryChat": "一時チャット", "resetChat": "チャットをリセット", "thisChatWontBeSaved": "このチャットは保存されません。", "feelFreeToAskAnythingTemporarily": "何でもお気軽に一時的にお尋ねください", "temporaryChatInstructions": "一時チャットの指示", "temporaryChatInstructionsPlaceholder": "こちらに指示を入力してください", "temporaryChatInstructionsDescription": "一時チャットの指示を設定できます。これは一時チャットのシステムプロンプトとして使用されます。"}, "placeholder": "何でも聞くか @mention してください", "Tool": {"webSearching": "ウェブを検索中...", "searchedTheWeb": "ウェブを検索しました", "toolModeDescription": "ツールの使用方法を選択:\n• 自動: AIがツールの使用を自動で決定\n• 手動: ツール使用前に許可を求める\n• なし: すべてのツールを無効化", "toolsSetupDescription": "チャットボットが使用できるツールを選択してください。\nチャットボットは選択されたツールを自分の判断で使用します。\n\n@mention を使って特定のツールの使用を強制することもできます。", "selectToolMode": "ツールモードを選択", "autoToolModeDescription": "あなたに尋ねることなく、ツールをいつ使用するかを決定します", "manualToolModeDescription": "ツールを使用する前にあなたの許可を求めます", "noneToolModeDescription": "ツールを使用しません。@mentionは引き続き利用可能です。", "toolsSetup": "ツール設定", "preset": "プリセット", "toolPresets": "ツールプリセット", "saveAsPreset": "プリセットとして保存", "saveAsPresetDescription": "現在のツール設定をプリセットとして保存します。", "noPresetsAvailableYet": "まだ利用可能なプリセットがありません", "presetNameCannotBeEmpty": "プリセット名を空にすることはできません", "presetNameAlreadyExists": "プリセット名が既に存在します", "presetSaved": "プリセットが保存されました", "clickSaveAsPresetToGetStarted": "開始するには「プリセットとして保存」をクリックしてください。", "searchOptions": "検索オプション", "searchOptionsDescription": "検索結果の最大数、検索日時など、チャットボットに検索オプションを渡すことができます。", "defaultToolKit": {"visualization": "データ可視化", "webSearch": "ウェブを検索"}}, "VoiceChat": {"title": "音声チャットモード", "compactDisplayMode": "コンパクト表示モード", "conversationDisplayMode": "会話表示モード", "pleaseCloseTheVoiceChatAndTryAgain": "音声チャットを閉じて、もう一度お試しください。", "startConversation": "会話を開始", "closeMic": "マイクを閉じる", "openMic": "マイクを開く", "endConversation": "会話を終了", "toggleVoiceChat": "音声チャットの切り替え", "readyWhenYouAreJustStartTalking": "準備ができました。話しかけてください。", "yourMicIsOff": "マイクがオフになっています。", "preparing": "準備中...", "startVoiceChat": "音声チャットを開始しますか？"}, "Thread": {"chat": "チャット", "renameChat": "名前を変更", "deleteChat": "チャットを削除", "deleteUnarchivedChats": "アーカイブされていないチャットをすべて削除", "confirmDeleteUnarchivedChats": "アーカイブされていないすべてのチャットを削除してもよろしいですか？", "thisActionCannotBeUndone": "この操作は元に戻すことができません。", "unarchivedChatsDeleted": "アーカイブされていないチャットが削除されました", "failedToDeleteUnarchivedChats": "アーカイブされていないチャットの削除に失敗しました", "failedToDeleteThread": "スレッドの削除に失敗しました", "threadDeleted": "スレッドが削除されました", "failedToUpdateThread": "スレッドの更新に失敗しました", "titleRequired": "タイトルが必要です", "threadUpdated": "スレッドが更新されました", "areYouSureYouWantToDeleteThisChatThread": "このチャットスレッドを削除してもよろしいですか？"}, "ChatPreferences": {"title": "チャット設定", "whatShouldWeCallYou": "何とお呼びすればよろしいですか？", "botName": "アシスタント名", "whatBestDescribesYourWork": "あなたの仕事を最もよく表すものは何ですか？", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "回答時に考慮すべき個人的な好みは何ですか？", "responseStyleExample1": "例：説明は簡潔で要点を押さえたものにしてください", "responseStyleExample2": "例：新しい概念を学ぶとき、類推が特に役立ちます", "responseStyleExample3": "例：詳細な回答をする前に明確化の質問をしてください", "responseStyleExample4": "例：私は主にPythonでプログラミングしています（プログラミング初心者ではありません）", "professionExample1": "例：ソフトウェアエンジニア", "professionExample2": "例：プロダクトマネージャー", "professionExample3": "例：マーケティングマネージャー", "professionExample4": "例：セールスマネージャー", "professionExample5": "例：ビジネスアナリスト", "preferencesSaved": "設定が保存されました", "failedToSavePreferences": "設定の保存に失敗しました", "userInstructions": "ユーザー指示", "userInstructionsDescription": "自己紹介をして、よりパーソナライズされた回答を受け取りましょう。", "mcpInstructions": "MCP 指示", "mcpInstructionsDescription": "MCP サーバーの指示をカスタマイズします。"}}, "Layout": {"toggleSidebar": "サイドバーの切り替え", "newChat": "新しいチャット", "mcpConfiguration": "MCP設定", "agents": "エージェント", "newAgent": "新しいエージェント", "createAgent": "エージェントを作成", "createYourOwnAgent": "独自の機能と個性を持つ専門AIエージェントを作成してみましょう", "whatIsAgent": "エージェントとは何ですか？", "agentDescription": "エージェントは、特定の役割、指示、ツールでカスタマイズできる専門的なAIアシスタントで、様々なタスクをサポートします。", "specializedAIAssistant": "専門AIアシスタント", "specializedAIAssistantDescription": "各エージェントは特定の役割、個性、専門分野でカスタマイズでき、あなた固有のニーズに焦点を当てたサポートを提供します。", "customInstructions": "カスタム指示", "customInstructionsDescription": "詳細なシステムプロンプトと行動ガイドラインを定義して、エージェントの応答と相互作用の方法を形作ることができます。", "toolIntegration": "ツール統合", "toolIntegrationDescription": "エージェントをMCPサーバー、ワークフロー、その他のツールに接続して、会話を超えた機能を拡張できます。", "agentExamples": "エージェントの例", "businessAssistant": "ビジネスアシスタント", "businessAssistantDescription": "ビジネス分析、レポート生成、プロフェッショナルなコミュニケーションに特化しています。", "creativeWriter": "クリエイティブライター", "creativeWriterDescription": "ストーリーテリング、コンテンツ制作、創造的なブレインストーミングに焦点を当てています。", "technicalExpert": "技術専門家", "technicalExpertDescription": "開発ツールとコーディングの専門知識を備えた技術的なタスクのためのエージェントです。", "createFirstAgentToStart": "最初のエージェントを作成して始めましょう！", "today": "今日", "yesterday": "昨日", "lastWeek": "過去7日間", "older": "それ以前", "recentChats": "最近のチャット", "deleteAllChats": "すべてのチャットを削除", "deleteUnarchivedChats": "アーカイブされていないチャットを削除", "noConversationsYet": "まだ会話がありません", "deletingAllChats": "すべてのスレッドを削除中...", "deletingUnarchivedChats": "アーカイブされていないスレッドを削除中...", "allChatsDeleted": "すべてのスレッドが削除されました", "unarchivedChatsDeleted": "アーカイブされていないスレッドが削除されました", "failedToDeleteAllChats": "すべてのスレッドの削除に失敗しました", "failedToDeleteUnarchivedChats": "アーカイブされていないスレッドの削除に失敗しました", "chatPreferences": "チャット設定", "keyboardShortcuts": "キーボードショートカット", "theme": "テーマ", "signOut": "サインアウト", "language": "言語", "showAllChats": "すべてのチャットを表示", "showLessChats": "少なく表示", "reportAnIssue": "問題を報告", "joinCommunity": "コミュニティに参加", "workflow": "ワークフロー"}, "Archive": {"title": "アーカイブ", "addArchive": "アーカイブを追加", "archiveName": "アーカイブ名", "archiveDescription": "アーカイブの説明", "archiveDescriptionPlaceholder": "アーカイブはチャット履歴を保存するスペースです。", "noArchives": "アーカイブがありません", "createFirstArchive": "最初のアーカイブを作成してください", "archiveCreated": "アーカイブが作成されました", "archiveUpdated": "アーカイブが更新されました", "archiveDeleted": "アーカイブが削除されました", "failedToCreateArchive": "アーカイブの作成に失敗しました", "failedToUpdateArchive": "アーカイブの更新に失敗しました", "failedToDeleteArchive": "アーカイブの削除に失敗しました", "editArchive": "アーカイブ編集", "editArchiveDescription": "アーカイブ情報を編集します", "deleteArchive": "アーカイブ削除", "confirmDeleteArchive": "本当にこのアーカイブを削除しますか？", "deleteArchiveDescription": "このアーカイブとすべてのアイテムが完全に削除されます。この操作は元に戻せません。", "addToArchive": "アーカイブに追加", "removeFromArchive": "アーカイブから削除", "itemAddedToArchive": "アイテムがアーカイブに追加されました", "itemRemovedFromArchive": "アイテムがアーカイブから削除されました"}, "Agent": {"title": "エージェント", "generatingAgent": "エージェント生成中...", "agentNameAndIconLabel": "エージェントに名前とアイコンを付けてください。", "agentDescriptionLabel": "このエージェントの用途についての簡単な説明を追加してください。", "agentDescriptionPlaceholder": "これはエージェントに関する説明であり、重要ではありません。", "agentSettingsDescription": "ここからは、エージェントに影響を与える可能性のある設定です。", "thisAgentIs": "このエージェントは", "expertIn": "の専門家です。", "agentRolePlaceholder": "株式分析", "agentInstructionsLabel": "このエージェントの役割、性格、指針、知識などを自由に書いてください。", "agentInstructionsPlaceholder": "このエージェントは株式分析をサポートします。ウェブ検索ツールを活用して株式情報を取得します...", "agentToolsLabel": "このエージェントが使用できるツールを追加してください。", "loadingTools": "ツールを読み込み中です...", "addTools": "ツールを追加してください。", "generateAgentGreeting": "こんにちは！あなた専用のエージェントを作成するお手伝いをします。何を作りたいですか？", "generateAgentDetailedGreeting": "こんにちは！あなた専用のエージェントを作成するお手伝いをします。何を作りたいですか？簡潔に書いても詳しく書いても構いません。", "inputPromptHere": "ここにプロンプトを入力してください...", "agentNamePlaceholder": "better-agent", "myAgents": "マイエージェント", "sharedAgents": "共有エージェント", "noAgents": "エージェントがありません", "createFirst": "最初のエージェントを作成して開始", "noSharedAgents": "共有エージェントがありません", "noSharedAgentsDescription": "ブックマーク可能な公開エージェントがありません", "noDescription": "説明が提供されていません", "bookmarkAdded": "エージェントをブックマークしました", "bookmarkRemoved": "ブックマークを削除しました", "bookmarkedAgent": "ブックマークしたエージェント", "addBookmark": "エージェントをブックマーク", "removeBookmark": "ブックマークを削除", "visibilityUpdated": "公開設定を更新しました", "deleted": "エージェントを削除しました", "deleteConfirm": "このエージェントを削除してもよろしいですか？", "makePrivate": "非公開にする", "makeReadonly": "読み取り専用にする", "makePublic": "公開する", "visibility": "公開設定", "private": "非公開", "readOnly": "読み取り専用", "public": "公開", "privateDescription": "あなただけがこのエージェントを表示、編集、使用できます。", "readOnlyDescription": "他の人もツールとして表示・使用できますが、編集はあなただけができます。", "publicDescription": "誰でもこのエージェントをツールとして表示、編集、使用できます。"}, "KeyboardShortcuts": {"title": "キーボードショートカット", "newChat": "新しいチャット", "toggleTemporaryChat": "一時チャットの切り替え", "toggleSidebar": "サイドバーの切り替え", "toolMode": "ツールモード", "lastMessageCopy": "最後のメッセージをコピー", "openChatPreferences": "チャット設定を開く", "deleteThread": "チャットを削除", "openShortcutsPopup": "ショートカットポップアップを開く", "toggleVoiceChat": "音声チャットの切り替え"}, "MCP": {"marketplace": "マーケットプレイス", "addMcpServer": "サーバーを追加", "configureYourMcpServerConnectionSettings": "MCPサーバー接続設定を構成してください", "mcpConfiguration": "MCP設定", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "名前は英数字（A-Z、a-z、0-9）とハイフン（-）のみを含む必要があります", "nameIsRequired": "名前が必要です", "configurationSavedSuccessfully": "設定が正常に保存されました", "enterMcpServerName": "MCPサーバー名を入力してください", "saveConfiguration": "設定を保存", "toolsTest": "ツールテスト", "refresh": "リフレッシュ", "delete": "削除", "edit": "編集", "configuration": "設定", "availableTools": "利用可能なツール", "noToolsAvailable": "利用可能なツールがありません", "overviewTitle": "最初のサーバーに接続", "overviewDescription": "強力なAI統合を解放するためにMCPサーバーを追加してください", "searchTools": "ツールを検索", "detail": "詳細", "noSchemaPropertiesAvailable": "利用可能なスキーマプロパティがありません", "createInputWithAI": "AIで入力を作成", "generateExampleInputJSON": "例の入力JSONを生成", "enterPromptToGenerateExampleInputJSON": "選択したツールの例の入力JSONを生成するためのプロンプトを入力してください。", "callTool": "ツールを呼び出す", "customInstructions": "カスタム指示", "serverCustomInstructionsPlaceholder": "このサーバーのツールが利用可能な場合、これらの行がシステムプロンプトに追加されます。", "nameAlreadyExists": "名前が既に存在します", "additionalInstructions": "ツールカスタマイゼーション指示", "inputSchema": "入力スキーマ", "toolCustomizationInstructions": "ツールカスタマイゼーション指示は、ツールが利用可能な場合にシステムプロンプトに追加されます。\n例）メールは常に*******************の形式で入力してください。", "mcpServerCustomization": "MCPサーバーカスタマイゼーション", "mcpServerCustomizationDescription": "MCPサーバーカスタマイゼーション指示は、MCPサーバーが利用可能な場合にシステムプロンプトに追加されます。", "toolCustomizationInstructionsPlaceholder": "ツールカスタマイゼーション指示は利用できません。", "mcpServerCustomizationPlaceholder": "例：入力値がメールの場合、常に*******************の形式でメールを入力してください。"}, "Error": {}, "Info": {"mcpAddingDisabled": "管理者によりMCPサーバーの追加が無効化されています。", "vercelSyncDelay": "Vercel上で動作中\n\nMCP変更の同期には10-15秒かかる場合があります。サーバーの追加、編集、または削除後に変更が即座に表示されない場合は、しばらくお待ちください。"}, "Workflow": {"title": "ワークフロー", "createWorkflow": "ワークフローを作成", "draft": "下書き", "publish": "公開", "createWorkflowDescription": "チャットボットの強力なツールとしてワークフローを作成します。", "workflowDescription": "これらは会話中にトリガーして複雑なタスクを自動化できます。", "nameAndIcon": "名前とアイコン", "workflowNamePlaceholder": "チャットボットはこれをツール名として認識します", "description": "説明", "descriptionPlaceholder": "チャットボットはこれをツールの説明として見ます", "inputNodeCannotBeDeleted": "入力ノードは削除できません", "autoSaveDescription": "10秒ごとに自動保存されます", "draftDescription": "現在は下書き状態です。\n\n公開をクリックしてチャットボットで利用可能にします\n（ただし編集できなくなります）。", "publishedDescription": "現在公開されており、チャットボットで利用可能です。\n\n下書きをクリックして編集可能にします\n（ただしチャットボットで利用できなくなります）。", "private": "プライベート", "readonly": "読み取り専用", "public": "公開", "privateDescription": "このワークフローを表示、編集、ツールとして使用できるのはあなただけです。", "readonlyDescription": "他の人は表示してツールとして使用できますが、編集できるのはあなただけです。", "publicDescription": "誰でもこのワークフローを表示、編集、ツールとして使用できます。", "visibilityDescription": "このワークフローにアクセスして変更できる人を制御します", "nodeDescriptionPlaceholder": "ノードの説明...", "nextNode": "次のノード", "nextNodeDescription": "このワークフローに次のノードを追加します。", "addNextNode": "次のノードを追加", "inputFields": "入力フィールド", "addInputField": "入力フィールドを追加", "inputFieldsDescription": "このワークフローのパラメータスキーマを定義します。\n\nチャットボットがこれをツールとして使用する際、\nこのスキーマに従って値を提供します。", "fieldEditor": "フィールドエディター", "variableName": "変数名", "variableNamePlaceholder": "変数名を入力...", "fieldDescriptionPlaceholder": "フィールドの説明を入力...", "defaultValuePlaceholder": "デフォルトの{type}値を入力...", "selectOptionPlaceholder": "オプションを選択...", "unlink": "ノードのリンクを解除", "elseIfDescription": "条件が満たされない場合、実行するロジックを定義します。", "elseDescription": "条件が満たされない場合、実行するロジックを定義します。", "addCondition": "条件を追加", "noVariablesFound": "変数が見つかりません", "outputVariables": "出力変数", "outputVariablesDescription": "ワークフローから出力される変数です。", "addOutputVariable": "出力変数を追加", "outputSchema": "出力スキーマ", "addMessage": "メッセージを追加", "messagesDescription": "LLM処理を通じてデータを生成します。\n\n'/'を使用して前のノードのデータを入力として言及・参照してください。\n\n構造化出力を有効にすると、データの変換、フォーマット、検証に最適です。", "descriptionAndSchema": "説明とスキーマ", "noDescriptionAndSchema": "説明とスキーマがありません", "toolDescription": "LLMがツールパラメータを生成するために必要な情報を提供します。\n\n'/'を使用して前のノードのデータを言及してください。", "generateInputWithAIDescription": "ワークフローの入力を生成するプロンプトを書いてください", "example": {"babyResearch": "ベビーリサーチ", "getWeather": "天気取得"}, "selectVariable": "変数を選択", "structuredOutput": "構造化出力", "structuredOutputDescription": "定義されたスキーマでJSONオブジェクトとしてレスポンスを生成", "outputSchemaEditor": "出力スキーマエディター", "addField": "フィールドを追加", "saveSchema": "スキーマを保存", "generateSchemaWithAI": "AIでスキーマを生成", "describeOutputDataRequest": "このノードが出力すべき内容を表すサンプルJSONデータを提供してください\n\n例: {eg}", "generatingJsonSchemaWithAI": "AIでJSONスキーマを生成中...", "jsonSchemaGeneratedSuccessfully": "JSONスキーマが正常に生成されました！", "failedToGenerateSchema": "スキーマの生成に失敗しました", "jsonSchemaEditorDescription": "AI支援による直接的なJSONスキーマ編集。複雑なネストした構造と配列をサポートします。", "template": "テンプレート", "templateDescription": "テンプレート文書を生成します。\n\n'/'を使用して他のノードの出力値を参照・使用してください。", "kindsDescription": {"input": "チャットボットがこのワークフローをツールとして使用する際に提供する入力パラメータを定義します。\n\nツール実行のためのデータ構造と検証ルールを指定します。", "output": "ワークフロー実行の最終結果を収集して返します。\n\n複数のノードからのデータを最終ツールレスポンスに結合します。", "llm": "AIモデルを使用してテキストや構造化データを生成します。\n\n'/'メンションで前のノード出力を参照してコンテキスト認識レスポンスを作成します。\n\n構造化出力を使用してデータの変換、フォーマット、検証を行う - 単なるテキスト生成ではありません。", "tool": "MCPツールや外部サービスを実行します。\n\nメッセージに指示を書けば、LLMがコンテキストから必要なツールパラメータを生成します。", "note": "ワークフローロジックを整理するためのドキュメントとコメントを追加します。\n\nチームメンバーが複雑なワークフロープロセスを理解するのに役立ちます。", "code": "前のノードデータにアクセスできるカスタムコードスクリプトを実行します。\n\nワークフロー内でJavaScript、Python、その他の言語を実行します（近日公開）。", "http": "HTTPリクエストを通じて外部APIやWebサービスからデータを取得します。\n\nREST API、Webhook、サードパーティサービスと統合します。", "template": "前のノードのデータとテキストを組み合わせて動的文書を作成します。\n\n変数置換を使用してメール、レポート、フォーマットされたコンテンツを生成します。", "condition": "データ評価に基づいてワークフローを分岐する条件ロジックを追加します。\n\n異なるシナリオやデータ条件を処理するif-elseロジックを作成します。"}, "greeting": {"buildAutomationTitle": "ノード接続による自動化構築", "buildAutomationDescription": "様々なノードを接続して複雑なタスクを自動化します。各ノードは特定の機能を担当し、データが順次流れて処理されます。", "chatbotToolTitle": "チャットボットツールとして活用", "chatbotToolDescription": "ワークフローの主な目的は、チャットボットの会話でツールとして使用することです。反復的なタスクをワークフローに変換して、チャット中に簡単に実行できます。", "parameterBasedTitle": "️ パラメータベースの開始", "parameterBasedDescription": "入力ノードはトリガーではなく、パラメータ構造を定義します。チャットボットがこのワークフローをツールとして呼び出すときに必要なデータ形式を指定します。", "exampleTitle": "使用例", "exampleDescription": "「メール作成 → 翻訳 → 送信」ワークフローを作成しておけば、チャットボットの会話で「@メール_ワークフロー」で簡単に実行できます。", "availableNodesTitle": "利用可能なノード", "upcomingNodesTitle": "近日公開予定のノード", "ctaMessage": "今すぐワークフローを作成してチャットボットの機能を拡張しましょう！", "soonMessage": "近日公開予定です。"}, "arrangeNodes": "自動レイアウト", "nodesArranged": "レイアウトが正常に適用されました", "visibilityUpdated": "公開設定が正常に更新されました"}}
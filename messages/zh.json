{"Common": {"cancel": "取消", "update": "更新", "continue": "继续", "success": "成功", "delete": "删除", "save": "保存", "back": "返回", "next": "下一步", "create": "创建", "showLess": "显示更少", "showMore": "显示更多", "generate": "生成", "edit": "编辑", "editAgent": "编辑代理", "search": "搜索...", "approve": "批准", "reject": "拒绝", "saving": "保存中...", "optional": "可选", "deleting": "删除中...", "run": "运行", "description": "描述", "defaultValue": "默认值", "empty": "空", "required": "必需", "options": "选项", "status": "状态", "result": "结果", "startedAt": "开始时间", "duration": "持续时间", "addOption": "添加选项", "tool": "工具", "selectTool": "选择工具...", "noResults": "无结果。", "generateInputWithAI": "用 AI 生成输入", "generatingInputWithAI": "正在用 AI 生成输入...", "inputGeneratedSuccessfully": "输入生成成功", "failedToGenerateInput": "输入生成失败", "createWithExample": "用示例创建", "generateWithAI": "用 AI 生成", "resultsFound": "找到 {count} 个结果", "youAreAnExpertIn": "您是{role}专家", "sharedBy": "{userName} 分享"}, "Auth": {"SignIn": {"title": "欢迎回来", "description": "登录您的账户以继续", "oauthClientIdNotSet": "{provider} 客户端 ID 未设置", "noAccount": "没有账户？", "signUp": "注册", "signIn": "登录", "orContinueWith": "或继续使用"}, "SignUp": {"title": "创建账户", "signIn": "登录", "description": "注册您的账户", "step1": "通过输入您的邮箱地址开始您的旅程", "step2": "我会在聊天时使用这个名字", "step3": "创建一个强密码来保护您的账户", "signUp": "注册", "invalidEmail": "无效的邮箱地址", "emailAlreadyExists": "邮箱已存在", "nameRequired": "姓名是必需的", "passwordRequired": "密码是必需的", "createAccount": "创建账户"}, "Intro": {"description": "欢迎来到 MCP 聊天机器人。登录以体验我们的 AI 驱动的对话工具。"}}, "Chat": {"Error": "聊天错误", "thisMessageWasNotSavedPleaseTryTheChatAgain": "此消息未保存。请重试聊天。", "Greeting": {"goodMorning": "早上好，{name}", "goodAfternoon": "下午好，{name}", "goodEvening": "晚上好，{name}", "niceToSeeYouAgain": "很高兴再次见到您，{name}", "whatAreYouWorkingOnToday": "您今天在做什么，{name}？", "letMeKnowWhenYoureReadyToBegin": "准备好开始时请告诉我。", "whatAreYourThoughtsToday": "您今天有什么想法？", "whereWouldYouLikeToStart": "您想从哪里开始？", "whatAreYouThinking": "您在想什么，{name}？"}, "TemporaryChat": {"toggleTemporaryChat": "切换临时聊天", "temporaryChat": "临时聊天", "resetChat": "重置聊天", "thisChatWontBeSaved": "此聊天不会被保存。", "feelFreeToAskAnythingTemporarily": "请随时临时询问任何问题", "temporaryChatInstructions": "临时聊天说明", "temporaryChatInstructionsPlaceholder": "在此输入您的说明", "temporaryChatInstructionsDescription": "您可以为临时聊天设置说明。这将用作临时聊天的系统提示。"}, "placeholder": "询问任何问题或 @mention", "Tool": {"webSearching": "正在搜索网络...", "searchedTheWeb": "搜索网络", "toolModeDescription": "选择工具使用方式:\n• 自动: AI 自动决定何时使用工具\n• 手动: 使用工具前询问许可\n• 无: 禁用所有工具", "toolsSetupDescription": "选择聊天机器人可以使用的工具。\n聊天机器人将根据自己的判断使用选定的工具。\n\n您也可以通过 @mention 强制使用特定工具。", "selectToolMode": "选择工具模式", "autoToolModeDescription": "自动决定何时使用工具，无需询问您", "manualToolModeDescription": "在使用任何工具前询问您的许可", "noneToolModeDescription": "不使用工具。@mention仍然可用。", "toolsSetup": "工具设置", "preset": "预设", "toolPresets": "工具预设", "saveAsPreset": "保存为预设", "saveAsPresetDescription": "将当前工具配置保存为预设。", "noPresetsAvailableYet": "暂无可用预设", "presetNameCannotBeEmpty": "预设名称不能为空", "presetNameAlreadyExists": "预设名称已存在", "presetSaved": "预设已保存", "clickSaveAsPresetToGetStarted": "点击保存为预设开始。", "searchOptions": "搜索选项", "searchOptionsDescription": "您可以向聊天机器人传递搜索选项，例如搜索结果的最大数量、搜索日期等。", "defaultToolKit": {"visualization": "数据可视化", "webSearch": "网页搜寻"}}, "VoiceChat": {"title": "语音聊天模式", "compactDisplayMode": "紧凑显示模式", "conversationDisplayMode": "对话显示模式", "pleaseCloseTheVoiceChatAndTryAgain": "请关闭语音聊天并重试。", "startConversation": "开始对话", "closeMic": "关闭麦克风", "openMic": "打开麦克风", "endConversation": "结束对话", "toggleVoiceChat": "切换语音聊天", "readyWhenYouAreJustStartTalking": "准备就绪—请开始说话。", "yourMicIsOff": "您的麦克风已关闭。", "preparing": "准备中...", "startVoiceChat": "开始语音聊天？"}, "Thread": {"chat": "聊天", "renameChat": "重命名", "deleteChat": "删除聊天", "deleteUnarchivedChats": "删除所有未归档的聊天", "confirmDeleteUnarchivedChats": "您确定要删除所有未归档的聊天吗？", "thisActionCannotBeUndone": "此操作无法撤销。", "unarchivedChatsDeleted": "未归档的聊天已删除", "failedToDeleteUnarchivedChats": "删除未归档聊天失败", "failedToDeleteThread": "删除线程失败", "threadDeleted": "线程已删除", "failedToUpdateThread": "更新线程失败", "titleRequired": "标题是必需的", "threadUpdated": "线程已更新", "areYouSureYouWantToDeleteThisChatThread": "您确定要删除此聊天线程吗？"}, "ChatPreferences": {"title": "聊天偏好", "whatShouldWeCallYou": "我们应该怎么称呼您？", "botName": "助手名称", "whatBestDescribesYourWork": "什么最能描述您的工作？", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "在回应中应该考虑哪些个人偏好？", "responseStyleExample1": "例如：保持解释简洁而切题", "responseStyleExample2": "例如：学习新概念时，我发现类比特别有用", "responseStyleExample3": "例如：在给出详细答案前先询问澄清问题", "responseStyleExample4": "例如：记住我主要用 Python 编程（不是编程初学者）", "professionExample1": "例如：软件工程师", "professionExample2": "例如：产品经理", "professionExample3": "例如：营销经理", "professionExample4": "例如：销售经理", "professionExample5": "例如：业务分析师", "preferencesSaved": "偏好已保存", "failedToSavePreferences": "保存偏好失败", "userInstructions": "用户说明", "userInstructionsDescription": "介绍自己并获得更个性化的回应。", "mcpInstructions": "MCP 说明", "mcpInstructionsDescription": "自定义 MCP 服务器说明。"}}, "Layout": {"toggleSidebar": "切换侧边栏", "newChat": "新聊天", "mcpConfiguration": "MCP 配置", "agents": "代理", "newAgent": "新代理", "createAgent": "创建代理", "createYourOwnAgent": "创建具有独特功能和个性的专业AI智能体", "whatIsAgent": "什么是代理？", "agentDescription": "代理是可以根据特定角色、指令和工具进行定制的专业AI助手，帮助您完成各种任务。", "specializedAIAssistant": "专业AI助手", "specializedAIAssistantDescription": "每个代理都可以根据特定角色、个性和专业领域进行定制，为您的独特需求提供专注的协助。", "customInstructions": "自定义指令", "customInstructionsDescription": "定义详细的系统提示和行为准则，塑造代理的响应和交互方式。", "toolIntegration": "工具集成", "toolIntegrationDescription": "将您的代理连接到MCP服务器、工作流程和其他工具，扩展其超越对话的能力。", "agentExamples": "代理示例", "businessAssistant": "商务助手", "businessAssistantDescription": "专门从事商业分析、报告生成和专业沟通。", "creativeWriter": "创意写手", "creativeWriterDescription": "专注于故事讲述、内容创作和创意头脑风暴。", "technicalExpert": "技术专家", "technicalExpertDescription": "配备开发工具和编码专业知识的技术任务代理。", "createFirstAgentToStart": "创建您的第一个代理开始吧！", "today": "今天", "yesterday": "昨天", "lastWeek": "最近 7 天", "older": "更早", "recentChats": "最近聊天", "deleteAllChats": "删除所有聊天", "deleteUnarchivedChats": "删除未归档聊天", "noConversationsYet": "暂无对话", "deletingAllChats": "正在删除所有线程...", "deletingUnarchivedChats": "正在删除未归档线程...", "allChatsDeleted": "所有线程已删除", "unarchivedChatsDeleted": "未归档线程已删除", "failedToDeleteAllChats": "删除所有线程失败", "failedToDeleteUnarchivedChats": "删除未归档线程失败", "chatPreferences": "聊天偏好", "keyboardShortcuts": "键盘快捷键", "theme": "主题", "signOut": "登出", "language": "语言", "showAllChats": "查看所有聊天", "showLessChats": "显示更少", "reportAnIssue": "报告问题", "joinCommunity": "加入社区", "workflow": "工作流"}, "Archive": {"title": "归档", "addArchive": "添加归档", "archiveName": "归档名称", "archiveDescription": "归档描述", "archiveDescriptionPlaceholder": "归档是存储聊天记录的空间。", "noArchives": "没有归档", "createFirstArchive": "创建您的第一个归档", "archiveCreated": "归档已创建", "archiveUpdated": "归档已更新", "archiveDeleted": "归档已删除", "failedToCreateArchive": "创建归档失败", "failedToUpdateArchive": "更新归档失败", "failedToDeleteArchive": "删除归档失败", "editArchive": "编辑归档", "editArchiveDescription": "编辑归档信息", "deleteArchive": "删除归档", "confirmDeleteArchive": "您确定要删除这个归档吗？", "deleteArchiveDescription": "此归档及其所有项目将被永久删除。此操作无法撤销。", "addToArchive": "添加到归档", "removeFromArchive": "从归档中移除", "itemAddedToArchive": "项目已添加到归档", "itemRemovedFromArchive": "项目已从归档中移除"}, "Agent": {"title": "智能体", "generatingAgent": "正在生成智能体...", "agentNameAndIconLabel": "为您的智能体设置名称和图标。", "agentDescriptionLabel": "添加此智能体用途的简短描述。", "agentDescriptionPlaceholder": "这只是对智能体的描述，并不重要。", "agentSettingsDescription": "以下是可能影响智能体的设置。", "thisAgentIs": "此智能体是", "expertIn": "专家。", "agentRolePlaceholder": "股票分析", "agentInstructionsLabel": "请自由描述此智能体的角色、性格、指导原则、知识等。", "agentInstructionsPlaceholder": "此智能体帮助进行股票分析。它使用网络搜索工具来获取股票信息...", "agentToolsLabel": "添加此智能体可以使用的工具。", "loadingTools": "正在加载工具...", "addTools": "请添加工具。", "generateAgentGreeting": "您好！我将帮助您创建您自己的智能体。您想创建什么？", "generateAgentDetailedGreeting": "您好！我将帮助您创建您自己的智能体。您想创建什么？您可以简要描述或详细描述。", "inputPromptHere": "在此输入提示词...", "agentNamePlaceholder": "better-agent", "myAgents": "我的智能体", "sharedAgents": "共享智能体", "noAgents": "还没有智能体", "createFirst": "创建您的第一个智能体开始使用", "noSharedAgents": "没有共享智能体", "noSharedAgentsDescription": "没有可书签的公共智能体", "noDescription": "未提供描述", "bookmarkAdded": "智能体已加入书签", "bookmarkRemoved": "书签已移除", "bookmarkedAgent": "已收藏的智能体", "addBookmark": "收藏智能体", "removeBookmark": "移除书签", "visibilityUpdated": "可见性已更新", "deleted": "智能体已删除", "deleteConfirm": "您确定要删除此智能体吗？", "makePrivate": "设为私有", "makeReadonly": "设为只读", "makePublic": "设为公开", "visibility": "可见性", "private": "私有", "readOnly": "只读", "public": "公开", "privateDescription": "只有您可以查看、编辑和使用此智能体。", "readOnlyDescription": "其他人可以查看并作为工具使用，但只有您可以编辑。", "publicDescription": "任何人都可以查看、编辑并将此智能体用作工具。"}, "KeyboardShortcuts": {"title": "键盘快捷键", "newChat": "新聊天", "toggleTemporaryChat": "切换临时聊天", "toggleSidebar": "切换侧边栏", "toolMode": "工具模式", "lastMessageCopy": "复制最后一条消息", "openChatPreferences": "打开聊天偏好", "deleteThread": "删除聊天", "openShortcutsPopup": "打开快捷键弹窗", "toggleVoiceChat": "切换语音聊天"}, "MCP": {"marketplace": "市场", "addMcpServer": "添加服务器", "configureYourMcpServerConnectionSettings": "配置您的 MCP 服务器连接设置", "mcpConfiguration": "MCP 配置", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "名称只能包含字母数字字符 (A-Z, a-z, 0-9) 和连字符 (-)", "nameIsRequired": "名称是必需的", "configurationSavedSuccessfully": "配置保存成功", "enterMcpServerName": "输入 MCP 服务器名称", "saveConfiguration": "保存配置", "toolsTest": "工具测试", "refresh": "刷新", "delete": "删除", "edit": "编辑", "configuration": "配置", "availableTools": "可用工具", "noToolsAvailable": "无可用工具", "overviewTitle": "连接您的第一个服务器", "overviewDescription": "添加 MCP 服务器以解锁强大的 AI 集成", "searchTools": "搜索工具", "detail": "详情", "noSchemaPropertiesAvailable": "无可用架构属性", "createInputWithAI": "用 AI 创建输入", "generateExampleInputJSON": "生成示例输入 JSON", "enterPromptToGenerateExampleInputJSON": "输入提示为所选工具生成示例输入 JSON。", "callTool": "调用工具", "customInstructions": "自定义指令", "serverCustomInstructionsPlaceholder": "当该服务器的任何工具可用时，这些内容将被添加到系统提示中。", "nameAlreadyExists": "名称已存在", "additionalInstructions": "工具自定义指令", "inputSchema": "输入架构", "toolCustomizationInstructions": "工具自定义指令将在工具可用时添加到系统提示中。\n示例）始终以 <EMAIL> 格式输入电子邮件。", "mcpServerCustomization": "MCP 服务器自定义", "mcpServerCustomizationDescription": "MCP 服务器自定义指令将在 MCP 服务器可用时添加到系统提示中。", "toolCustomizationInstructionsPlaceholder": "工具自定义指令不可用。", "mcpServerCustomizationPlaceholder": "例如：如果输入值是电子邮件，始终以 <EMAIL> 格式输入电子邮件。"}, "Error": {}, "Info": {"mcpAddingDisabled": "管理员已禁用MCP服务器添加功能。", "vercelSyncDelay": "在Vercel上运行\n\nMCP更改同步可能需要10-15秒。如果添加、编辑或删除服务器后更改未立即显示，请稍等片刻。"}, "Workflow": {"title": "工作流", "createWorkflow": "创建工作流", "draft": "草稿", "publish": "发布", "createWorkflowDescription": "为您的聊天机器人创建强大的工具工作流。", "workflowDescription": "这些可以在对话中触发以自动化复杂任务。", "nameAndIcon": "名称和图标", "workflowNamePlaceholder": "聊天机器人将识别此为工具名称", "description": "描述", "descriptionPlaceholder": "聊天机器人将看到此作为工具描述", "inputNodeCannotBeDeleted": "输入节点无法删除", "autoSaveDescription": "每10秒自动保存", "draftDescription": "当前处于草稿状态。\n\n点击发布使其对聊天机器人可用\n（但不再可编辑）。", "publishedDescription": "当前已发布并对聊天机器人可用。\n\n点击草稿使其可编辑\n（但对聊天机器人不可用）。", "private": "私有", "readonly": "只读", "public": "公开", "privateDescription": "只有您可以查看、编辑和使用此工作流作为工具。", "readonlyDescription": "其他人可以查看和用作工具，但只有您可以编辑。", "publicDescription": "任何人都可以查看、编辑和使用此工作流作为工具。", "visibilityDescription": "控制谁可以访问和修改此工作流", "nodeDescriptionPlaceholder": "节点描述...", "nextNode": "下一个节点", "nextNodeDescription": "为此工作流添加下一个节点。", "addNextNode": "添加下一个节点", "inputFields": "输入字段", "addInputField": "添加输入字段", "inputFieldsDescription": "定义此工作流的参数架构。\n\n当聊天机器人使用此作为工具时，\n它将根据此架构提供值。", "fieldEditor": "字段编辑器", "variableName": "变量名", "variableNamePlaceholder": "输入变量名...", "fieldDescriptionPlaceholder": "输入字段描述...", "defaultValuePlaceholder": "输入默认 {type} 值...", "selectOptionPlaceholder": "选择选项...", "unlink": "取消链接节点", "elseIfDescription": "如果条件不满足，定义要执行的逻辑。", "elseDescription": "如果条件不满足，定义要执行的逻辑。", "addCondition": "添加条件", "noVariablesFound": "未找到变量", "outputVariables": "输出变量", "outputVariablesDescription": "从工作流输出的变量。", "addOutputVariable": "添加输出变量", "outputSchema": "输出架构", "addMessage": "添加消息", "messagesDescription": "通过 LLM 处理生成数据。\n\n使用 '/' 提及和引用前一个节点的数据作为输入。\n\n启用结构化输出时，非常适合数据转换、格式化和验证。", "descriptionAndSchema": "描述和架构", "noDescriptionAndSchema": "无描述和架构", "toolDescription": "提供 LLM 生成工具参数所需的信息。\n\n使用 '/' 提及前一个节点的数据。", "generateInputWithAIDescription": "编写提示为工作流生成输入", "example": {"babyResearch": "婴儿研究", "getWeather": "获取天气"}, "selectVariable": "选择变量", "structuredOutput": "结构化输出", "structuredOutputDescription": "使用定义的模式生成 JSON 对象响应", "outputSchemaEditor": "输出模式编辑器", "addField": "添加字段", "saveSchema": "保存模式", "generateSchemaWithAI": "用 AI 生成模式", "describeOutputDataRequest": "提供代表此节点应输出内容的示例 JSON 数据\n\n示例: {eg}", "generatingJsonSchemaWithAI": "正在用 AI 生成 JSON 模式...", "jsonSchemaGeneratedSuccessfully": "JSON 模式生成成功！", "failedToGenerateSchema": "模式生成失败", "jsonSchemaEditorDescription": "在 AI 辅助下直接编辑 JSON 模式。支持复杂的嵌套结构和数组。", "template": "模板", "templateDescription": "生成模板文档。\n\n使用 '/' 引用和使用其他节点的输出值。", "kindsDescription": {"input": "定义聊天机器人将此工作流用作工具时提供的输入参数。\n\n指定工具执行的数据结构和验证规则。", "output": "收集并返回工作流执行的最终结果。\n\n将多个节点的数据合并为最终工具响应。", "llm": "使用 AI 模型生成文本或结构化数据。\n\n使用 '/' 提及引用前一个节点的输出，创建上下文感知的响应。\n\n使用结构化输出进行数据转换、格式化和验证 - 不仅仅是文本生成。", "tool": "执行 MCP 工具或外部服务。\n\n在消息中写入指令，LLM 将从您的上下文生成所需的工具参数。", "note": "添加文档和注释来组织您的工作流逻辑。\n\n帮助团队成员理解复杂的工作流过程。", "code": "执行可访问前一个节点数据的自定义代码脚本。\n\n在工作流中运行 JavaScript、Python 或其他语言（即将推出）。", "http": "通过 HTTP 请求从外部 API 和 Web 服务获取数据。\n\n与 REST API、webhook 和第三方服务集成。", "template": "通过将文本与前一个节点的数据结合来创建动态文档。\n\n使用变量替换生成电子邮件、报告或格式化内容。", "condition": "基于数据评估添加条件逻辑来分支您的工作流。\n\n创建 if-else 逻辑来处理不同的场景和数据条件。"}, "greeting": {"buildAutomationTitle": "通过连接节点构建自动化", "buildAutomationDescription": "连接各种节点来自动化复杂任务。每个节点处理特定功能，数据按顺序流动处理。", "chatbotToolTitle": "用作聊天机器人工具", "chatbotToolDescription": "工作流的主要目的是在聊天机器人对话中用作工具。将重复性任务转换为工作流，在聊天中轻松执行。", "parameterBasedTitle": "️ 基于参数的开始", "parameterBasedDescription": "输入节点定义参数结构，而不是触发器。它们指定聊天机器人将此工作流用作工具时所需的数据格式。", "exampleTitle": "使用示例", "exampleDescription": "创建一个\"邮件编写 → 翻译 → 发送\"工作流，然后在聊天机器人对话中用\"@邮件_工作流\"轻松执行。", "availableNodesTitle": "可用节点", "upcomingNodesTitle": "即将推出的节点", "ctaMessage": "现在就开始创建工作流来扩展您的聊天机器人能力！", "soonMessage": "即将推出。"}, "arrangeNodes": "自动布局", "nodesArranged": "布局已成功应用", "visibilityUpdated": "可见性已成功更新"}}
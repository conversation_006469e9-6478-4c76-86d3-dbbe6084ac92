{"Common": {"cancel": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "continue": "<PERSON><PERSON><PERSON><PERSON>", "success": "Éxito", "delete": "Eliminar", "save": "Guardar", "back": "Atrás", "next": "Siguient<PERSON>", "create": "<PERSON><PERSON><PERSON>", "showLess": "<PERSON><PERSON> menos", "showMore": "Mostrar más", "generate": "Generar", "edit": "<PERSON><PERSON>", "editAgent": "<PERSON><PERSON>", "search": "Buscar...", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "saving": "Guardando...", "optional": "Opcional", "deleting": "Eliminando...", "run": "<PERSON><PERSON><PERSON><PERSON>", "description": "Descripción", "defaultValue": "<PERSON><PERSON>er<PERSON>", "empty": "Vacío", "required": "Requerido", "options": "Opciones", "status": "Estado", "result": "<PERSON><PERSON><PERSON><PERSON>", "startedAt": "Iniciado En", "duration": "Duración", "addOption": "Agregar Opción", "tool": "Herramienta", "selectTool": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "noResults": "Sin resultados.", "generateInputWithAI": "Generar Entrada con IA", "generatingInputWithAI": "Generando entrada con IA...", "inputGeneratedSuccessfully": "Entrada generada exitosamente", "failedToGenerateInput": "Error al generar entrada", "createWithExample": "<PERSON><PERSON><PERSON> con <PERSON>", "resultsFound": "{count} resultados encontrados", "youAreAnExpertIn": "<PERSON>res un experto en {role}", "sharedBy": "Compartido por {userName}"}, "Auth": {"SignIn": {"title": "Bienvenido de vuelta", "description": "Inicia sesión para continuar a tu cuenta", "oauthClientIdNotSet": "ID del cliente de {provider} no configurado", "noAccount": "¿No tienes una cuenta?", "signUp": "Registrarse", "signIn": "In<PERSON><PERSON>", "orContinueWith": "O CONTINÚA CON"}, "SignUp": {"title": "<PERSON><PERSON><PERSON> una cuenta", "signIn": "In<PERSON><PERSON>", "description": "Regístrate en tu cuenta", "step1": "Comienza tu viaje con nosotros ingresando tu dirección de correo electrónico", "step2": "Usaré este nombre cuando charlemos", "step3": "Crea una contraseña segura para proteger tu cuenta", "signUp": "Registrarse", "invalidEmail": "Dirección de correo electrónico inválida", "emailAlreadyExists": "El correo electrónico ya existe", "nameRequired": "El nombre es obligatorio", "passwordRequired": "La contraseña es obligatoria", "createAccount": "<PERSON><PERSON><PERSON> cuenta"}, "Intro": {"description": "Bienvenido a better-chatbot. Inicia sesión para experimentar nuestras herramientas conversacionales impulsadas por IA."}}, "Chat": {"Error": "<PERSON><PERSON><PERSON>", "thisMessageWasNotSavedPleaseTryTheChatAgain": "Este mensaje no se guardó. Por favor, intenta el chat nuevamente.", "Greeting": {"goodMorning": "Buenos días, {name}", "goodAfternoon": "<PERSON>uenas tardes, {name}", "goodEvening": "Buenas noches, {name}", "niceToSeeYouAgain": "Es un placer verte de nuevo, {name}", "whatAreYouWorkingOnToday": "¿En qué estás trabajando hoy, {name}?", "letMeKnowWhenYoureReadyToBegin": "Avísame cuando estés listo para comenzar.", "whatAreYourThoughtsToday": "¿<PERSON><PERSON><PERSON><PERSON> son tus pensamientos hoy?", "whereWouldYouLikeToStart": "¿Por dónde te gustaría empezar?", "whatAreYouThinking": "¿Qué estás pensando, {name}?"}, "TemporaryChat": {"toggleTemporaryChat": "Alternar Chat Temporal", "temporaryChat": "Chat <PERSON>", "resetChat": "<PERSON><PERSON><PERSON><PERSON>", "thisChatWontBeSaved": "Este chat no se guardará.", "feelFreeToAskAnythingTemporarily": "Siéntete libre de preguntar cualquier cosa temporalmente", "temporaryChatInstructions": "Instrucciones del Chat Temporal", "temporaryChatInstructionsPlaceholder": "Ingresa tus instrucciones aquí", "temporaryChatInstructionsDescription": "<PERSON>uedes establecer instrucciones para el chat temporal. Esto se usará como prompt del sistema para el chat temporal."}, "placeholder": "Pregunta cualquier cosa o @menciona", "Tool": {"webSearching": "Buscando en la web...", "searchedTheWeb": "Buscado en la web", "toolModeDescription": "<PERSON>je cómo se usan las herramientas:\n• Auto: La IA decide cuándo usar herramientas\n• Manual: Pide permiso antes de usar herramientas\n• Ninguno: Desactiva todas las herramientas", "toolsSetupDescription": "Selecciona las herramientas que el chatbot puede usar.\nEl chatbot utilizará las herramientas seleccionadas según su propio juicio.\n\nTambién puedes forzar el uso de herramientas específicas a través de @mention.", "selectToolMode": "Seleccionar modo de herramientas", "autoToolModeDescription": "Decide cuándo usar herramientas sin preguntarte", "manualToolModeDescription": "Pide tu permiso antes de usar cualquier herramienta", "noneToolModeDescription": "No usar herramientas. @mention sigue estando disponible.", "toolsSetup": "Configuración de Herramientas", "preset": "<PERSON><PERSON><PERSON><PERSON>", "toolPresets": "Preajustes de Herramientas", "saveAsPreset": "Guardar como Preajuste", "saveAsPresetDescription": "Guarda la configuración actual de herramientas como un preajuste.", "noPresetsAvailableYet": "Aún no hay preajustes disponibles", "presetNameCannotBeEmpty": "El nombre del preajuste no puede estar vacío", "presetNameAlreadyExists": "El nombre del preajuste ya existe", "presetSaved": "<PERSON><PERSON><PERSON><PERSON> guardado", "clickSaveAsPresetToGetStarted": "Haz clic en Guardar como Preajuste para comenzar.", "searchOptions": "Opciones de Búsqueda", "searchOptionsDescription": "Puedes pasar opciones de búsqueda al chatbot, como el número máximo de resultados de búsqueda, la fecha de búsqueda, etc.", "defaultToolKit": {"visualization": "Visualización de datos", "webSearch": "Buscar en la web"}}, "VoiceChat": {"title": "<PERSON><PERSON> por Voz", "compactDisplayMode": "Modo de visualización compacta", "conversationDisplayMode": "Modo de visualización de conversación", "pleaseCloseTheVoiceChatAndTryAgain": "Por favor, cierra el chat por voz e intenta de nuevo.", "startConversation": "Iniciar conversación", "closeMic": "<PERSON><PERSON><PERSON><PERSON>", "openMic": "<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>", "endConversation": "Terminar conversación", "toggleVoiceChat": "Alternar Chat por Voz", "readyWhenYouAreJustStartTalking": "Listo cuando lo estés—solo comienza a hablar.", "yourMicIsOff": "Tu micrófono está apagado.", "preparing": "Preparando...", "startVoiceChat": "¿Iniciar chat por voz?"}, "Thread": {"chat": "Cha<PERSON>", "renameChat": "Renombrar", "deleteChat": "Eli<PERSON><PERSON>", "deleteUnarchivedChats": "Eliminar Todos los Chats No Archivados", "confirmDeleteUnarchivedChats": "¿Estás seguro de que quieres eliminar todos los chats no archivados?", "thisActionCannotBeUndone": "Esta acción no se puede deshacer.", "unarchivedChatsDeleted": "Los chats no archivados han sido eliminados", "failedToDeleteUnarchivedChats": "Error al eliminar chats no archivados", "failedToDeleteThread": "Error al eliminar el hilo", "threadDeleted": "<PERSON><PERSON> eliminado", "failedToUpdateThread": "Error al actualizar el hilo", "titleRequired": "El título es obligatorio", "threadUpdated": "<PERSON><PERSON>", "areYouSureYouWantToDeleteThisChatThread": "¿Estás seguro de que quieres eliminar este hilo de chat?"}, "ChatPreferences": {"title": "Preferencias de Chat", "whatShouldWeCallYou": "¿Cómo deberíamos llamarte?", "botName": "Nombre del Asistente", "whatBestDescribesYourWork": "¿Qué describe mejor tu trabajo?", "whatPersonalPreferencesShouldBeTakenIntoAccountInResponses": "¿Qué preferencias personales deberían considerarse en las respuestas?", "responseStyleExample1": "ej. mantén las explicaciones breves y al grano", "responseStyleExample2": "ej. al aprender nuevos conceptos, encuentro las analogías particularmente útiles", "responseStyleExample3": "ej. haz preguntas aclaratorias antes de dar respuestas detalladas", "responseStyleExample4": "ej. recuerda que programo principalmente en Python (no soy principiante en programación)", "professionExample1": "ej. ingeniero de software", "professionExample2": "ej. gerente de producto", "professionExample3": "ej. gerente de marketing", "professionExample4": "ej. gere<PERSON> de ventas", "professionExample5": "ej. analista de negocios", "preferencesSaved": "Preferencias guardadas", "failedToSavePreferences": "Error al guardar las preferencias", "userInstructions": "Instrucciones de Usuario", "userInstructionsDescription": "Preséntate y obtén una respuesta más personalizada.", "mcpInstructions": "Instrucciones MCP", "mcpInstructionsDescription": "Personaliza las instrucciones del servidor MCP."}}, "Layout": {"toggleSidebar": "Alternar Barra Lateral", "newChat": "Nuevo Chat", "mcpConfiguration": "Configuración MCP", "agents": "<PERSON><PERSON>", "newAgent": "Nuevo Agente", "createAgent": "<PERSON>rear un agente", "createYourOwnAgent": "Crea tu propio agente de IA especializado con características y personalidad únicas", "whatIsAgent": "¿Qué es un Agente?", "agentDescription": "Los agentes son asistentes de IA especializados que pueden personalizarse con roles, instrucciones y herramientas específicas para ayudarte con diversas tareas.", "specializedAIAssistant": "Asistente de IA Especializado", "specializedAIAssistantDescription": "Cada agente puede personalizarse con roles específicos, personalidades y áreas de experiencia para brindar asistencia enfocada en tus necesidades únicas.", "customInstructions": "Instrucciones Personalizadas", "customInstructionsDescription": "Define prompts de sistema detallados y pautas de comportamiento para dar forma a cómo tu agente responde e interactúa contigo.", "toolIntegration": "Integración de Herramientas", "toolIntegrationDescription": "Conecta tus agentes a servidores MCP, flujos de trabajo y otras herramientas para extender sus capacidades más allá de la conversación.", "agentExamples": "<PERSON>je<PERSON>los de Agentes", "businessAssistant": "Asistente de Negocios", "businessAssistantDescription": "Especializado en análisis empresarial, generación de informes y comunicación profesional.", "creativeWriter": "Escritor Creativo", "creativeWriterDescription": "Enfocado en narrativa, creación de contenido y lluvia de ideas creativas.", "technicalExpert": "Experto Técnico", "technicalExpertDescription": "Equipado con herramientas de desarrollo y experiencia en codificación para tareas técnicas.", "createFirstAgentToStart": "¡Crea tu primer agente para empezar!", "today": "Hoy", "yesterday": "Ayer", "lastWeek": "Últimos 7 días", "older": "<PERSON>ás anti<PERSON>", "recentChats": "Chats Recientes", "deleteAllChats": "Eliminar Todos los Chats", "deleteUnarchivedChats": "Eliminar Chats No Archivados", "noConversationsYet": "Aún no hay conversaciones", "deletingAllChats": "Eliminando todos los hilos...", "deletingUnarchivedChats": "Eliminando hilos no archivados...", "allChatsDeleted": "Todos los hilos eliminados", "unarchivedChatsDeleted": "Hilos no archivados eliminados", "failedToDeleteAllChats": "Error al eliminar todos los hilos", "failedToDeleteUnarchivedChats": "Error al eliminar hilos no archivados", "chatPreferences": "Preferencias de Chat", "keyboardShortcuts": "Atajos de Teclado", "theme": "<PERSON><PERSON>", "signOut": "<PERSON><PERSON><PERSON>", "language": "Idioma", "showAllChats": "Ver Todos los Chats", "showLessChats": "<PERSON><PERSON> menos", "reportAnIssue": "Reportar un problema", "joinCommunity": "Unirse a la Comunidad", "workflow": "Flujo de Trabajo"}, "Archive": {"title": "Archivo", "addArchive": "Añadir Archivo", "archiveName": "Nombre del Archivo", "archiveDescription": "Descripción del Archivo", "archiveDescriptionPlaceholder": "Los archivos son espacios para almacenar el historial de chat.", "noArchives": "No hay archivos", "createFirstArchive": "Crea tu primer archivo", "archiveCreated": "Archivo creado", "archiveUpdated": "Archivo actualizado", "archiveDeleted": "Archivo eliminado", "failedToCreateArchive": "Error al crear archivo", "failedToUpdateArchive": "Error al actualizar archivo", "failedToDeleteArchive": "Error al eliminar archivo", "editArchive": "Editar Archivo", "editArchiveDescription": "Editar información del archivo", "deleteArchive": "Eliminar Archivo", "confirmDeleteArchive": "¿Está seguro de que desea eliminar este archivo?", "deleteArchiveDescription": "Este archivo y todos sus elementos se eliminarán permanentemente. Esta acción no se puede deshacer.", "addToArchive": "<PERSON><PERSON>dir al Archivo", "removeFromArchive": "Eliminar del Archivo", "itemAddedToArchive": "Elemento añadido al archivo", "itemRemovedFromArchive": "Elemento eliminado del archivo"}, "Agent": {"title": "<PERSON><PERSON>", "generatingAgent": "Generando Agente...", "agentNameAndIconLabel": "<PERSON> a tu agente un nombre y un icono.", "agentDescriptionLabel": "Añade una breve descripción de lo que hace este agente.", "agentDescriptionPlaceholder": "Esto es solo una descripción del agente, no es crítico.", "agentSettingsDescription": "A partir de aquí, estas son configuraciones que pueden afectar al agente.", "thisAgentIs": "Este agente es un experto en", "expertIn": "", "agentRolePlaceholder": "análisis de acciones", "agentInstructionsLabel": "Siéntete libre de escribir el papel, personalidad, directrices, conocimiento, etc. del agente.", "agentInstructionsPlaceholder": "Este agente ayuda con el análisis de acciones. Utiliza herramientas de búsqueda web para obtener información de acciones...", "agentToolsLabel": "Añade herram<PERSON>as que este agente puede usar.", "loadingTools": "<PERSON><PERSON><PERSON>...", "addTools": "Por favor a<PERSON><PERSON>.", "generateAgentGreeting": "¡Hola! Te ayudaré a crear tu propio agente. ¿Qué te gustaría crear?", "generateAgentDetailedGreeting": "¡Hola! Te ayudaré a crear tu propio agente. ¿Qué te gustaría crear? Puedes escribir de forma breve o detallada.", "inputPromptHere": "escribe el prompt aquí...", "agentNamePlaceholder": "better-agent", "myAgents": "<PERSON><PERSON> Agentes", "sharedAgents": "Agentes Co<PERSON>", "noAgents": "Aún no hay agentes", "createFirst": "Crea tu primer agente para empezar", "noSharedAgents": "No hay agentes compartidos", "noSharedAgentsDescription": "No hay agentes públicos disponibles para marcar", "noDescription": "No se proporcionó descripción", "bookmarkAdded": "<PERSON><PERSON> marcado", "bookmarkRemoved": "<PERSON><PERSON> eliminado", "bookmarkedAgent": "<PERSON><PERSON> marcado", "addBookmark": "Marcar agente", "removeBookmark": "Eliminar marcador", "visibilityUpdated": "Visibilidad actualizada", "deleted": "Agente eliminado", "deleteConfirm": "¿Estás seguro de que quieres eliminar este agente?", "makePrivate": "<PERSON><PERSON>", "makeReadonly": "<PERSON><PERSON> Lect<PERSON>", "makePublic": "<PERSON><PERSON>", "visibility": "Visibilidad", "private": "Privado", "readOnly": "Solo Lectura", "public": "Público", "privateDescription": "Solo tú puedes ver, editar y usar este agente.", "readOnlyDescription": "Otros pueden ver y usar como herramienta, pero solo tú puedes editar.", "publicDescription": "Cualquiera puede ver, editar y usar este agente como herramienta."}, "KeyboardShortcuts": {"title": "Atajos de Teclado", "newChat": "Nuevo Chat", "toggleTemporaryChat": "Alternar Chat Temporal", "toggleSidebar": "Alternar Barra Lateral", "toolMode": "<PERSON><PERSON>", "lastMessageCopy": "<PERSON><PERSON><PERSON>", "openChatPreferences": "<PERSON><PERSON><PERSON>", "deleteThread": "Eli<PERSON><PERSON>", "openShortcutsPopup": "<PERSON><PERSON><PERSON> Ataj<PERSON>", "toggleVoiceChat": "Alternar Chat por Voz"}, "MCP": {"marketplace": "<PERSON><PERSON><PERSON>", "addMcpServer": "<PERSON><PERSON><PERSON><PERSON>", "configureYourMcpServerConnectionSettings": "Configura los ajustes de conexión de tu servidor MCP", "mcpConfiguration": "Configuración MCP", "nameMustContainOnlyAlphanumericCharactersAndHyphens": "El nombre debe contener solo caracteres alfanuméricos (A-Z, a-z, 0-9) y guiones (-)", "nameIsRequired": "El nombre es obligatorio", "configurationSavedSuccessfully": "Configuración guardada exitosamente", "enterMcpServerName": "Ingresa el nombre del servidor MCP", "saveConfiguration": "Guardar Configuración", "toolsTest": "Prueba de Herramientas", "refresh": "Actualizar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "configuration": "Configuración", "availableTools": "Herramientas Disponibles", "noToolsAvailable": "No hay herramientas disponibles", "overviewTitle": "Conecta Tu Primer Servidor", "overviewDescription": "Agrega servidores MCP para desbloquear integraciones de IA poderosas", "searchTools": "Buscar herramientas", "detail": "Detalle", "noSchemaPropertiesAvailable": "No hay propiedades de esquema disponibles", "createInputWithAI": "Crear Entrada con IA", "generateExampleInputJSON": "Generar JSON de Entrada de Ejemplo", "enterPromptToGenerateExampleInputJSON": "Ingresa un prompt para generar un JSON de entrada de ejemplo para la herramienta seleccionada.", "callTool": "<PERSON><PERSON><PERSON> a herram<PERSON>a", "customInstructions": "Instrucciones personalizadas", "serverCustomInstructionsPlaceholder": "Estas líneas se añadirán al mensaje del sistema siempre que haya una herramienta de este servidor disponible.", "nameAlreadyExists": "El nombre ya existe", "additionalInstructions": "Instrucciones de Personalización de Herramientas", "inputSchema": "Esquema de Entrada", "toolCustomizationInstructions": "Las instrucciones de personalización de herramientas se añadirán al prompt del sistema cuando la herramienta esté disponible.\nejemplo) Siempre ingresa el email en <NAME_EMAIL>.", "mcpServerCustomization": "Personalización del Servidor MCP", "mcpServerCustomizationDescription": "Las instrucciones de personalización del servidor MCP se añadirán al prompt del sistema cuando el servidor MCP esté disponible.", "toolCustomizationInstructionsPlaceholder": "Las instrucciones de personalización de herramientas no están disponibles.", "mcpServerCustomizationPlaceholder": "ej. <PERSON> el valor de entrada es email, siempre ingresa el email en <NAME_EMAIL>."}, "Error": {}, "Info": {"mcpAddingDisabled": "La adición de servidores MCP ha sido deshabilitada por el administrador.", "vercelSyncDelay": "Ejecutándose en Vercel\n\nLos cambios de MCP pueden tardar 10-15 segundos en sincronizarse. Por favor espera un momento si los cambios no aparecen inmediatamente después de agregar, editar o eliminar servidores."}, "Workflow": {"title": "Flujo de Trabajo", "createWorkflow": "<PERSON><PERSON><PERSON> Trabajo", "draft": "<PERSON><PERSON><PERSON>", "publish": "Publicar", "createWorkflowDescription": "<PERSON>rea flujos de trabajo como herramientas poderosas para tu chatbot.", "workflowDescription": "Estos pueden activarse durante conversaciones para automatizar tareas complejas.", "nameAndIcon": "Nombre e Icono", "workflowNamePlaceholder": "El chatbot reconocerá esto como nombre de herramienta", "description": "Descripción", "descriptionPlaceholder": "El chatbot verá esto como descripción de herramienta", "inputNodeCannotBeDeleted": "El nodo de entrada no puede ser eliminado", "autoSaveDescription": "Guardado automático cada 10 segundos", "draftDescription": "Actualmente en Borrador.\n\nHaz clic en Publicar para hacerlo disponible al chatbot\n(pero ya no será editable).", "publishedDescription": "Actualmente Publicado y disponible para el chatbot.\n\nHaz clic en Borrador para hacerlo editable\n(pero no disponible para el chatbot).", "private": "Privado", "readonly": "Solo Lectura", "public": "Público", "privateDescription": "Solo tú puedes ver, editar y usar este flujo de trabajo como herramienta.", "readonlyDescription": "Otros pueden ver y usar como herramienta, pero solo tú puedes editar.", "publicDescription": "Cualquiera puede ver, editar y usar este flujo de trabajo como herramienta.", "visibilityDescription": "Controla quién puede acceder y modificar este flujo de trabajo", "nodeDescriptionPlaceholder": "descripción del nodo...", "nextNode": "Siguiente Nodo", "nextNodeDescription": "Agregar un siguiente nodo a este flujo de trabajo.", "addNextNode": "Ag<PERSON><PERSON>", "inputFields": "Campos de Entrada", "addInputField": "Agregar Campo de Entrada", "inputFieldsDescription": "Define el esquema de parámetros para este flujo de trabajo.\n\n<PERSON>uando el chatbot use esto como herramienta,\nproporcionará valores según este esquema.", "fieldEditor": "Editor de Campo", "variableName": "Nombre de Variable", "variableNamePlaceholder": "Ingresa nombre de variable...", "fieldDescriptionPlaceholder": "Ingresa descripción del campo...", "defaultValuePlaceholder": "Ingresa valor {type} predeterminado...", "selectOptionPlaceholder": "Seleccionar opción...", "unlink": "Des<PERSON><PERSON>", "elseIfDescription": "Si la condición no se cumple, se define la lógica a ejecutar.", "elseDescription": "Si la condición no se cumple, se define la lógica a ejecutar.", "addCondition": "Agregar Condición", "noVariablesFound": "No se encontraron variables", "outputVariables": "Variables de Salida", "outputVariablesDescription": "Las variables de salida son las variables que se generan desde el flujo de trabajo.", "addOutputVariable": "Agregar Variable de Salida", "outputSchema": "Esquema de Salida", "addMessage": "<PERSON><PERSON><PERSON><PERSON>", "messagesDescription": "Generar datos a través del procesamiento LLM.\n\nUsa '/' para mencionar y referenciar datos de nodos anteriores como entrada.\n\nCon Salida Estructurada habilitada, perfecto para transformación, formateo y validación de datos.", "descriptionAndSchema": "Descripción y Esquema", "noDescriptionAndSchema": "Sin descripción y esquema", "toolDescription": "Proporciona información necesaria para que LLM genere parámetros de herramienta.\n\nUsa '/' para mencionar datos de nodos anteriores.", "generateInputWithAIDescription": "Escribe un prompt para generar entrada para el flujo de trabajo", "example": {"babyResearch": "Investigación de Bebés", "getWeather": "Consultar Clima"}, "selectVariable": "Seleccionar Variable", "structuredOutput": "Salida Estructurada", "structuredOutputDescription": "Generar respuesta como objeto JSON con esquema definido", "outputSchemaEditor": "Editor de Esquema de Salida", "addField": "Agregar <PERSON>", "saveSchema": "Guardar Esquema", "generateSchemaWithAI": "Generar Esquema con IA", "describeOutputDataRequest": "Proporciona datos JSON de ejemplo que representen lo que este nodo debería generar\n\nEjemplo: {eg}", "generatingJsonSchemaWithAI": "Generando esquema JSON con IA...", "jsonSchemaGeneratedSuccessfully": "¡Esquema JSON generado exitosamente!", "failedToGenerateSchema": "Error al generar esquema", "jsonSchemaEditorDescription": "Edición directa de esquema JSON con asistencia de IA. Soporta estructuras anidadas complejas y arrays.", "template": "Plantilla", "templateDescription": "Generar documentos de plantilla.\n\nUsa '/' para referenciar y usar valores de salida de otros nodos.", "kindsDescription": {"input": "Define parámetros de entrada que el chatbot proporcionará al usar este flujo de trabajo como herramienta.\n\nEspecifica la estructura de datos y reglas de validación para la ejecución de la herramienta.", "output": "Recopila y retorna los resultados finales de la ejecución de tu flujo de trabajo.\n\nCombina datos de múltiples nodos en la respuesta final de la herramienta.", "llm": "Genera texto o datos estructurados usando modelos de IA.\n\nReferencia salidas de nodos anteriores con menciones '/' para crear respuestas conscientes del contexto.\n\nUsa Salida Estructurada para transformar, formatear y validar datos - no solo para generación de texto.", "tool": "Ejecuta herramientas MCP o servicios externos.\n\nEscribe instrucciones en mensajes, y LLM generará los parámetros de herramienta requeridos desde tu contexto.", "note": "Agrega documentación y comentarios para organizar la lógica de tu flujo de trabajo.\n\nAyuda a los miembros del equipo a entender procesos complejos de flujo de trabajo.", "code": "Ejecuta scripts de código personalizado con acceso a datos de nodos anteriores.\n\nEjecuta JavaScript, Python u otros lenguajes dentro de tu flujo de trabajo (próximamente).", "http": "Obtén datos de APIs externas y servicios web vía peticiones HTTP.\n\nIntégrate con APIs REST, webhooks y servicios de terceros.", "template": "Crea documentos dinámicos combinando texto con datos de nodos anteriores.\n\n<PERSON>ra corre<PERSON>, reportes o contenido formateado usando sustitución de variables.", "condition": "Agrega lógica condicional para ramificar tu flujo de trabajo basado en evaluación de datos.\n\nCrea lógica if-else para manejar diferentes escenarios y condiciones de datos."}, "greeting": {"buildAutomationTitle": "Construir Automatización Conectando Nodos", "buildAutomationDescription": "Conecta varios nodos para automatizar tareas complejas. Cada nodo maneja funciones específicas, y los datos fluyen secuencialmente para su procesamiento.", "chatbotToolTitle": "Usar como Herramientas de Chatbot", "chatbotToolDescription": "El propósito principal de los flujos de trabajo es usarlos como herramientas en conversaciones de chatbot. Convierte tareas repetitivas en flujos de trabajo para una fácil ejecución durante los chats.", "parameterBasedTitle": "️ Inicio Basado en Parámetros", "parameterBasedDescription": "Los nodos de entrada definen estructuras de parámetros, no disparadores. Especifican el formato de datos necesario cuando el chatbot llama a este flujo de trabajo como herramienta.", "exampleTitle": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>o", "exampleDescription": "Crea un flujo de trabajo \"Escribir <PERSON>ail → Traducir → Enviar\", luego eje<PERSON><PERSON><PERSON>o fácilmente en conversaciones de chatbot con \"@email_workflow\".", "availableNodesTitle": "Nodos Disponibles", "upcomingNodesTitle": "Nodos Próximos", "ctaMessage": "¡Comienza a crear flujos de trabajo ahora para expandir las capacidades de tu chatbot!", "soonMessage": "Próximamente."}, "arrangeNodes": "Auto Layout", "nodesArranged": "Layout aplicado exitosamente", "visibilityUpdated": "Visibilidad actualizada exitosamente"}}
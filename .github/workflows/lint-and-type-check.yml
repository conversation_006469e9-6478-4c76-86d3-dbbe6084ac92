name: <PERSON><PERSON> and <PERSON> Check

on:
  pull_request:
    branches: [main]

jobs:
  lint_and_type_check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run Lint
        run: pnpm lint

      - name: Run Type Check
        run: pnpm check-types

      - name: Run Tests
        run: pnpm test

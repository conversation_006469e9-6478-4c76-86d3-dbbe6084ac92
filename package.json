{"name": "better-chatbot", "version": "1.21.0", "private": true, "author": "cgoinglove", "license": "MIT", "type": "module", "scripts": {"dev": "next dev --turbopack", "dev:https": "next dev --turbopack --experimental-https", "build": "next build", "start": "next start", "build:local": "cross-env NO_HTTPS='1' next build", "test": "vitest run", "test:watch": "vitest", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "check-types": "tsc --noEmit", "initial:env": "tsx scripts/initial-env.ts", "openai-compatiable:init": "tsx scripts/init-openai-compatiable.ts", "openai-compatiable:parse": "tsx scripts/parse-openai-compatiable.ts", "postinstall": "tsx scripts/postinstall.ts", "clean": "tsx scripts/clean.ts", "db:generate": "drizzle-kit generate", "db:reset": "drizzle-kit drop && drizzle-kit push", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:migrate": "tsx scripts/db-migrate.ts", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "docker-compose:up": "docker-compose -f docker/compose.yml up -d --build", "docker-compose:down": "docker-compose -f docker/compose.yml down", "docker-compose:logs": "docker-compose -f docker/compose.yml logs -f", "docker-compose:ps": "docker-compose -f docker/compose.yml ps", "docker-compose:update": "git pull && docker-compose -f docker/compose.yml up -d --build", "docker:pg": "docker run --name better-chatbot-pg -e POSTGRES_PASSWORD=your_password -e POSTGRES_USER=your_username -e POSTGRES_DB=your_database_name -p 5432:5432 -d postgres", "docker:redis": "docker run --name better-chatbot-redis -p 6379:6379 -d redis:7-alpine", "docker:app": "docker build -f docker/Dockerfile -t better-chatbot . && docker run -p 3000:3000 -e NO_HTTPS=1 better-chatbot", "prepare": "husky", "playwright:install": "playwright install", "check": "pnpm lint:fix && pnpm check-types && pnpm test"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.8", "@ai-sdk/google": "^2.0.11", "@ai-sdk/openai": "^2.0.22", "@ai-sdk/openai-compatible": "^1.0.13", "@ai-sdk/react": "^2.0.26", "@ai-sdk/xai": "^2.0.13", "@modelcontextprotocol/sdk": "^1.17.4", "@openrouter/ai-sdk-provider": "^1.1.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.8", "@tiptap/extension-mention": "^2.26.1", "@tiptap/react": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@tiptap/suggestion": "^2.26.1", "@xyflow/react": "^12.8.4", "ai": "^5.0.26", "bcrypt-ts": "^7.1.0", "better-auth": "^1.3.7", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "consola": "^3.4.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "dotenv": "^16.6.1", "drizzle-orm": "^0.41.0", "emoji-picker-react": "^4.13.2", "framer-motion": "^12.23.12", "hast-util-to-jsx-runtime": "^2.3.6", "ioredis": "^5.7.0", "json-schema": "^0.4.0", "lucide-react": "^0.486.0", "mermaid": "^11.10.1", "nanoid": "^5.1.5", "next": "15.3.2", "next-intl": "^4.3.5", "next-themes": "^0.4.6", "ogl": "^1.0.11", "ollama-ai-provider-v2": "^1.2.1", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "server-only": "^0.0.1", "shiki": "^3.12.0", "sonner": "^2.0.7", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "ts-edge": "^1.0.4", "ts-safe": "^0.0.5", "tw-animate-css": "^1.3.7", "vaul": "^1.1.2", "zod": "^4.1.4", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/json-schema": "^7.0.15", "@types/node": "^20.19.11", "@types/pg": "^8.15.5", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.8", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "eslint": "^9.34.0", "eslint-config-next": "15.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "rimraf": "^6.0.1", "tailwindcss": "^4.1.12", "tsx": "^4.20.5", "typescript": "^5.9.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "lint-staged": {"*.{js,json,mjs,ts,yaml,tsx,css}": ["pnpm format", "pnpm lint:fix"]}, "packageManager": "pnpm@10.2.1", "engines": {"node": ">=18"}, "pnpm": {"onlyBuiltDependencies": ["@biomejs/biome", "@tailwindcss/oxide", "esbuild", "sharp", "unrs-resolver"]}}